﻿
<template>
	<view>
		<view class="invoice-head">
			<u-tabs 
					:list="invoiceTab" 
					:scrollable="false"
				  lineWidth="150rpx"
					lineHeight='5'
					lineColor="#f56c6c"
					 	:activeStyle="{
					 	color: '#f14e4e',
					 }"
					 	:inactiveStyle="{
					 	color: '#2f2f2f',
					 }"
					 @click="tabsTap"
					>
			</u-tabs>
		</view>
		<view class="service-charge d-bf" v-show="this.BillData.amount_type === 2">
			<view class="service-item">
				<view class="price">{{completedAmount.toFixed(2) || 0.00}}</view>
				<view class="title">已开票技术服务费</view>
			</view>
			<view class="service-item">
				<view class="price">{{expireAmount.toFixed(2) || 0.00}}</view>
				<view class="title">未开票技术服务费</view>
			</view>
			<view class="service-item">
				<view class="price">{{waitMakeAmount.toFixed(2) || 0.00}}</view>
				<view class="title">已过期技术服务费</view>
			</view>
		</view>
		<view class="invoice-tab-list d-bf">
			<block v-for="(item,index) in invoiceTabList" :key="index">
				<view :class="tabIndex === index?'on':''" @click="invoiceTabOn(index,item.name)">{{item.name}}</view>
			</block>
		</view>
		<view class="invoice-msg-list" v-if="billList.length > 0">
			<block v-for="(item,index) in billList" :key="index" >
				<view class="invoice-msg-item" v-if="nameState !== '未开票' &&  nameState !== '已过期'">
					<view class="msg-item-title">
						{{item.created_at}}
					</view>
					<u-line></u-line>
					<view class="item-content">
						<view class="item-type" v-if="item.type === 1">发票类型：电子普通发票</view>
						<view class="item-type" v-if="item.type === 2">发票类型：增值税专用发票</view>
						<view class="item-type" v-if="item.account_type === 1">发票抬头：{{item.person_name}}</view>
						<view class="item-type" v-if="item.account_type === 2">发票抬头：{{item.company_name}}</view>
						<view class="item-type">发票金额：￥{{item.amount.toFixed(2) || 0.00}}</view>
						<view class="item-type" v-if="item.drawer">开票主体：供应商开票</view>
						<view class="item-type" v-else>开票主体：平台开票</view>
					</view>
					<u-line></u-line>
					<view class="item-footer d-ef">
						<view 
							class="confirm footer-btn" 
							v-if="BillData.status === 1"
							@click="invoiceConfirm(item.id,2)"
						>
							确定
						</view>
						<view 
							class="refuse footer-btn" 
							v-if="BillData.status === 1"
							@click="invoiceConfirm(item.id,0)"
						>
							拒绝
						</view>
						<view class="lookBtn"  v-if="nameState == '未开票'">申请开票</view>
						<view class="lookBtn" v-else @click="navTo('/packageC/invoiceDetails/invoiceDetails?id=' + item.id)">查看</view>
					</view>
				</view>
			</block>
		</view>
		<view class="empty" v-else>
			<u-empty 
			        mode="order" marginTop="100rpx" textSize="28rpx" iconSize="150">
			</u-empty>
		</view>
		
		<invoice-details
			v-if="nameState === '未开票' ||  nameState === '已过期'"
			:details="billList"
			:amount_type="BillData.amount_type"
			:nameState="nameState"
			:billBatchIds="billIds"
			:setting="settingBill"
			@olidatedInvoice="invoiceChane"
			@refresh="refreshPage"
			ref="invoiceHandle"
			>
		</invoice-details>
		<view v-if="isReachBottom && !$isEmpty.isEmpty(billList)" class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
					暂无更多~
		</view>
		<view class="mb155"></view>
		<pay-together 
			v-show="nameState === '未开票' && checkAll && merge"
			@allBillBtnTest="allBillBtn"
			>
		</pay-together> <!-- UI按钮组件 -->
		
		
	</view>
</template>

<script>
			import eventBus from '@/utils/eventBus';
	export default {
				provide() { //会出现变量提升问题，父组件访问不到传给孙组件的数据
			return {
				bill_type: () => this.billType, //孙组件数据
			}
		},
		data() {
			return {
				invoiceTab:[
					{
						name:'订单发票',
						type:1,
					},
					{
						name:'技术服务发票',
						type:2,
					}
				],
				invoiceTabList:[
					{
						name:'待开票',
						type:0
					},
					{
						name:'待确认',
						type:1
					},
					{
						name:'已开票',
						type:2
					},
					{
						name:'未开票',
						type:0
					},
					{
						name:'已过期',
						type:1
					}
				],
				merge:null,
				nameState:'',
				invoiceUrl:'',
				billList:[],
				billType:[], //发票类型，判断是否开发票
				billIds:[], //批量发票数组
				settingBill:{}, //发票所有设置
				completedAmount:0, //已开票技术服务费
				expireAmount:0,//已过期技术服务费
				waitMakeAmount:0, //未开票技术服务费
				checkAll:false,
				tabIndex:0,
				isReachBottom: false, //列表到底
				BillData:{
					amount_type:1,
					status:0,
					page:1,
					pageSize:10,
				},
				total:0,
				expire_status:0 //已过期
				
			}
		},
		onReachBottom(){
			let  allTotal = Number(this.BillData.page) * Number(this.BillData.pageSize);
			let allTotalInvoice =  Math.ceil(allTotal);
				if(allTotal < this.total){
					this.status = 'loading';  //加载中状态
					//当前条数小于总条数 则增加请求页数
					this.BillData.page ++;
					this.invoiceApiUrl();
					this.getBillList(this.invoiceUrl);
				}else{
					this.status = 'nomore'; //加载完状态
					// console.log('已加载全部数据')
					this.isReachBottom = true;
				}
		},
		onPullDownRefresh() {
			this.billList = [];
			this.BillData.page = 1;
			//调用获取数据方法
			setTimeout(() => {
				//结束下拉刷新
			  uni.stopPullDownRefresh();
			}, 1000);
		},
		onShow(){
			this.BillData.page = 1;
			this.billList =[];
			this.invoiceApiUrl();
			this.getBillList(this.invoiceUrl);
			this.findTradeSetting();
			this.techFeeStatistic();
		},
		methods: {
			invoiceTabOn(index,name) { //tab栏目状态切换
				this.tabIndex = index;
				this.billList =[];
				this.nameState = name;
				this.checkAll = false;
				this.BillData.page = 1;
				if(name === '未开票') {
					this.BillData.status = 0;
					this.notInvoicedUrl(this.BillData.amount_type);
				} else if(name === '已过期') {
					this.expire_status = 1;
					this.billExpireUrl(this.BillData.amount_type);
				} else {
					this.BillData.status = index;
					this.invoiceUrl = `/api/bill/getBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				}
				this.getBillList(this.invoiceUrl);
			},
			tabsTap(item) { //不同发票的切换
				
				this.billList =[];
				this.BillData.page = 1;
				this.BillData.amount_type = item.type;
				
				if(this.nameState === '未开票') {
					this.BillData.status = 0;
					this.notInvoicedUrl(item.type);
					
				} else if(this.nameState === '已过期') {
					this.expire_status = 1;
					this.billExpireUrl(item.type);
					
				} else {
					this.invoiceUrl = `/api/bill/getBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				}
				this.getBillList(this.invoiceUrl);
			},
			invoiceConfirm(id,status) {
				this.post('/api/bill/confirmBill', {id,status}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.toast(res.msg);
						
					} else {
						this.toast(res.msg);
					}
					this.billList =[];
					this.invoiceUrl = `/api/bill/getBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
					setTimeout(() => {
						this.getBillList(this.invoiceUrl);
					},500);
				}).catch((Error) => {
					console.log(Error);
				})
			},
			invoiceApiUrl() {  //切换状态不同请求不同的api || this.nameState === '已过期'
				if(this.nameState === '未开票') {
					this.notInvoicedUrl(this.BillData.amount_type);
				} else if (this.nameState === '已过期' ) {
					this.billExpireUrl(this.BillData.amount_type);
				} else {
					this.invoiceUrl = `/api/bill/getBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				}
			},
			billExpireUrl(type) { //技术服务费已过期列表获取
				if(type == 2) {
					this.invoiceUrl = `/api/bill/getTechFeeBillList?amount_type=${this.BillData.amount_type}&expire_status=${this.expire_status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				} else {
					this.invoiceUrl = `/api/bill/getOrderBillList?amount_type=${this.BillData.amount_type}&expire_status=${this.expire_status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				}
			},
			notInvoicedUrl(type) { //技术服务费未开票列表获取
				if(type == 2) {
					this.invoiceUrl = `/api/bill/getTechFeeBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				} else {
					this.invoiceUrl = `/api/bill/getOrderBillList?amount_type=${this.BillData.amount_type}&status=${this.BillData.status}&page=${this.BillData.page}&pageSize=${this.BillData.pageSize}`;
				}
			},
			refreshPage() {
				this.billList =[];
				this.checkAll = false;
				this.invoiceApiUrl();
				this.getBillList(this.invoiceUrl);
			},
			invoiceChane(id,index) {
				let billIds = [];
				this.billList[index].checked = !this.billList[index].checked;
				for(let i in this.billList) { //遍历查询ID合并开发票
					if(this.billList[i].checked) {
						billIds.push(this.billList[i].id);
					}
				}
				this.billIds = [...new Set(billIds)]; 
				this.checkAll = this.billList.some(item => item.checked === true); //检测是否选中
			},
			getBillList(url) { //发票列表
				this.get(url,{}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let  BillList = data.list;
						for(let i in BillList) {
							BillList[i].amount = this.toYuan(BillList[i].amount);
							BillList[i].created_at = this.formatDateTime(BillList[i].created_at,6);
							this.$set(BillList[i],'checked',false); //判断是否选中
							if(this.nameState === '未开票' || this.nameState === '已过期') {
								BillList[i].order.amount = this.toYuan(BillList[i].order.amount);
								let orderItems = BillList[i].order.order_items;
								for(let j in orderItems) {
									orderItems[j].amount = this.toYuan(orderItems[j].amount);
								}
							}
						}
						
						this.billList.push(...BillList);
						this.total = data.total;
					} else {
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			findTradeSetting() { //获取发票设置
				this.get('/api/bill/findTradeSetting', {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.merge = data.setting?.value.merge;
						this.settingBill = data.setting?.value;
						return this.billType = data.setting?.value.bill_type;
					} else {
						
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			techFeeStatistic() {
				this.get('/api/bill/techFeeStatistic', {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.completedAmount = this.toYuan(data.statistic.completed_amount);
						this.expireAmount = this.toYuan(data.statistic.expire_amount);
						this.waitMakeAmount = this.toYuan(data.statistic.wait_make_amount);
					} else {
						
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			allBillBtn() {
				console.log('1111');
				this.$nextTick(() => {
					this.$refs.invoiceHandle.invoiceShow = true; //修改子组件的值
					this.$refs.invoiceHandle.overBillShow = true; //修改子组件的值
					this.$refs.invoiceHandle.orderBill.amount_type = this.BillData.amount_type; //修改发票类型
					eventBus.$emit('overlay', 10070); //设置发票详情的遮罩层
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.invoice-head{
		background-color: #fff;
	}
	.service-charge {
		background-color: #fff;
		padding: 22rpx 18rpx 20rpx 18rpx;
		.service-item {
			text-align: center;
			font-size: 24rpx;
			.price {
				color: #101010;
				margin-bottom: 20rpx;
			}
			.title {
				color:#B8B8B8;
			}
		}
	}
	.invoice-tab-list {
		margin: 33rpx 20rpx 32rpx 20rpx;
		view {
			padding: 13rpx 25rpx;
			color: #5c5c5c;
			font-size: 24rpx;
		}
		.on {
			background-color: #f14e4e;
			border-radius: 28rpx;
			color: #5c5c5c;
			color: #ffffff;
		}
	}
	.invoice-msg-list {
		margin: 0 20rpx;
		.invoice-msg-item {
			background-color: #fff;
			padding: 0 30rpx;
			margin-bottom: 20rpx;
			.msg-item-title {
				padding: 30rpx 0 31rpx 0;
				font-size: 22rpx;
				color: #828282;
			}
			.item-content {
				padding:30rpx 0 2rpx 0;
				.item-type {
					color: #2f2f2f;
					font-size: 26rpx;
					margin-bottom: 28rpx;
				}
			}
			.item-footer {
				padding: 20rpx 0;
				.lookBtn {
					padding: 8rpx 36rpx;
					color: #f14e4e;
					font-size: 24rpx;
					border: solid 2rpx #f14e4e;
					border-radius: 28rpx;
				}
				.confirm {
					background-color: rgba(243, 42, 42, 100);
				}
				.refuse {
					background-color: rgba(255, 170, 79, 100);
				}
				.footer-btn {
					padding: 8rpx 36rpx;
					border-radius: 28rpx;
					color: #FFFFFF;
					margin-right: 10rpx;
				}
			}
		}
	}
	
</style>

