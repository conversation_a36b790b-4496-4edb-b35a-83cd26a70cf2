{
  "easycom": {
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "",
        "usingComponents": {
          "my-move-goods": "/my-components/components/myMoveGoods",
          "my-move-album": "/my-components/components/myMoveAlbum",
          "my-move-suppliers": "/my-components/components/myMoveSuppliers",
          "my-to-top": "/my-components/components/myToTop",
          "my-tabbar": "/my-components/components/myTabbar"
        }
      }
    },
    {
      "path": "pages/classify/classify",
      "style": {
        "navigationBarTitleText": "分类",
        //#ifdef H5
        "navigationStyle": "custom",
        //#endif
        "enablePullDownRefresh": false,
        "usingComponents": {
          "my-classify-module": "/my-components/components/myClassifyModule",
          "my-tabbar": "/my-components/components/myTabbar"
        }
      }
    },
    {
      "path": "pages/promotion/promotion",
      "style": {
        "navigationBarTitleText": "推广中心",
        "enablePullDownRefresh": false,
        "navigationBarBackgroundColor": "#F15353",
        "navigationBarTextStyle": "white",
        "usingComponents": {
          "my-unlogged-page": "/my-components/components/myUnloggedPage",
          "my-promotion-list": "/my-components/components/myPromotionList",
          "my-tabbar": "/my-components/components/myTabbar"
        }
      }
    },
    {
      "path": "pages/shoping_car/shoping_car",
      "style": {
        "navigationBarTitleText": "购物车",
        "enablePullDownRefresh": false,
        "usingComponents": {
          "my-unlogged-page": "/my-components/myUnloggedPage",
          "my-tabbar": "/my-components/myTabbar"
        }
      }
    },
    {
      "path": "pages/membercenter/membercenter",
      "style": {
        "navigationBarTitleText": "会员中心",
        "enablePullDownRefresh": false,
        "navigationBarBackgroundColor": "#F15353",
        "navigationBarTextStyle": "white",
        "usingComponents": {
          "my-order": "/my-components/myOrder",
          "my-beauty": "/my-components/myBeauty",
          "my-small": "/my-components/mySmall",
          "my-tabbar": "/my-components/myTabbar"
        }
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "用户登录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/register/register",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "my-components",
      "pages": [
        {
          "path": "pages/index",
          "style": {
            "navigationBarTitleText": "公共组件库",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      //商品列表（搜索页）、商品详情、下单页、支付页、订单列表、订单详情
      "root": "packageA", //根目录下的分包文件路径
      //pages里面一样写法不能放tabbar页面
      "pages": [
        {
          "path": "commodity/commodity_details/commodity_details",
          "style": {
            "navigationBarTitleText": "商品详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "search/search",
          "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "search/searchResult/searchResult",
          "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "myOrder/myOrder",
          "style": {
            "navigationBarTitleText": "我的订单",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "goodsorder/goodsorder",
          "style": {
            "navigationBarTitleText": "商品订单详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "goodsorder/orderpay/orderpay",
          "style": {
            "navigationBarTitleText": "订单支付",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "goodsorder/juHeOrderPay/juHeOrderPay",
          "style": {
            "navigationBarTitleText": "订单支付",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "myOrder/orderDetail/orderDetail",
          "style": {
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "goodsorder/orderpay/webView",
          "style": {
            "navigationBarTitleText": "订单支付",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "packageB", //根目录下的分包文件路径
      "pages": [
        {
          "path": "member/address",
          "style": {
            "navigationBarTitleText": "地址管理",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/applyAgency",
          "style": {
            "navigationBarTitleText": "申请代理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/supplier",
          "style": {
            "navigationBarTitleText": "申请供应商",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/aftersaleslist",
          "style": {
            "navigationBarTitleText": "售后列表",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/aftersales",
          "style": {
            "navigationBarTitleText": "售后详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/alterAddress",
          "style": {
            "navigationBarTitleText": "修改收货地址",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/appendAddress",
          "style": {
            "navigationBarTitleText": "添加收货地址",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/refund",
          "style": {
            "navigationBarTitleText": "售后申请",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "member/evaluateList",
          "style": {
            "navigationBarTitleText": "选择售后商品",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/logistics",
          "style": {
            "navigationBarTitleText": "物流详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "wallet/wallet",
          "style": {
            "navigationBarTitleText": "我的钱包",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "goods/commentDetails/commentDetails",
          "style": {
            "navigationBarTitleText": "全部评价",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "goods/commentDetails/evaluationDetails/evaluationDetails",
          "style": {
            "navigationBarTitleText": "评价详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "store/storeDetails",
          "style": {
            "navigationBarTitleText": "店铺名称",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "notice/noticeCategory",
          "style": {
            "navigationBarTitleText": "文章列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "notice/noticeDetails",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "notice/noticeList",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "user/personalData/personalData",
          "style": {
            "navigationBarTitleText": "个人资料",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "user/collect/collect",
          "style": {
            "navigationBarTitleText": "个人收藏",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "user/myCustomer/myCustomer",
          "style": {
            "navigationBarTitleText": "我的客户",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/orderCommentDetails",
          "style": {
            "navigationBarTitleText": "评论详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/evaluate",
          "style": {
            "navigationBarTitleText": "评价",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/bindBank",
          "style": {
            "navigationBarTitleText": "绑定银行卡",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/negotiationRecord",
          "style": {
            "navigationBarTitleText": "协商记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/recharge",
          "style": {
            "navigationBarTitleText": "充值",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/juHeRecharge",
          "style": {
            "navigationBarTitleText": "充值",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/withdraw",
          "style": {
            "navigationBarTitleText": "提现",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "user/shareOutBonus/shareOutBonus",
          "style": {
            "navigationBarTitleText": "区域代理中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "user/distribution/distribution",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "webView/webView",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "expressage/expressage",
          "style": {
            "navigationBarTitleText": "填写快递",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "packageC", //根目录下的分包文件路径
      "pages": [
        {
          "path": "invoice/invoice",
          "style": {
            "navigationBarTitleText": "我的发票",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "invoiceDetails/invoiceDetails",
          "style": {
            "navigationBarTitleText": "发票详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/bulkOrder",
          "style": {
            "navigationBarTitleText": "批量下单",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "widescreenAdaptation/widescreenAdaptation",
          "style": {
            "app-plus": {
              "titleNView": false
            } //去掉
          }
        },
        {
          "path": "membershipApplication/membershipApplication",
          "style": {
            "navigationBarTitleText": "会员申请",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "album/albumList",
          "style": {
            "navigationBarTitleText": "共享专辑",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "album/albumDetail",
          "style": {
            "navigationBarTitleText": "共享专辑详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "handpickClassify/handpickClassify",
          "style": {
            "navigationBarTitleText": "精选分类",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "packageD", //根目录下的分包文件路径
      "pages": [
        {
          "path": "memberLevel/memberRight",
          "style": {
            "navigationBarTitleText": "会员权益",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "memberLevel/memberPayment",
          "style": {
            "navigationBarTitleText": "会员付费升级",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "memberLevel/memberPayRecord",
          "style": {
            "navigationBarTitleText": "开通记录",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "smallShop/smallShopManage",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "smallShop/storeSetting",
          "style": {
            "navigationBarTitleText": "店铺设置",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/productManage",
          "style": {
            "navigationBarTitleText": "商品管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/albumManage",
          "style": {
            "navigationBarTitleText": "专辑管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/albumDetail",
          "style": {
            "navigationBarTitleText": "专辑详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/productSelection",
          "style": {
            "navigationBarTitleText": "在线选品",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/productSetting",
          "style": {
            "navigationBarTitleText": "选品设置",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/smallShopAudit",
          "style": {
            "navigationBarTitleText": "小商店审核中",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/smallShopOrder",
          "style": {
            "navigationBarTitleText": "小商店订单",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/smallShopAfterSales",
          "style": {
            "navigationBarTitleText": "小商店售后",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/smallShopAfterDetail",
          "style": {
            "navigationBarTitleText": "小商店售后详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/smallShopOrderDetail",
          "style": {
            "navigationBarTitleText": "小商店订单详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "shopList/shopList",
          "style": {
            "navigationBarTitleText": "全部店铺",
            "onReachBottomDistance": 5,
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/liveStreamManage",
          "style": {
            "navigationBarTitleText": "直播管理"
          }
        },
        {
          "path": "poster/list",
          "style": {
            "navigationBarTitleText": "海报中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "curriculum/curriculum_details",
          "style": {
            "navigationBarTitleText": "课程详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "lecturer/lecturer_details",
          "style": {
            "navigationBarTitleText": "讲师主页",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "smallShop/courseManage",
          "style": {
            "navigationBarTitleText": "课程管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agreement/agreement",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/distributorsAndShops",
          "style": {
            "navigationBarTitleText": "团队分销商和小商店",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/myShop",
          "style": {
            "navigationBarTitleText": "我的小商店",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/popularizePoster",
          "style": {
            "navigationBarTitleText": "推广海报",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/distributor",
          "style": {
            "navigationBarTitleText": "分销商付费升级",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/distributorPayment",
          "style": {
            "navigationBarTitleText": "代理升级",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agencies/distributorPayRecord",
          "style": {
            "navigationBarTitleText": "开通记录",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#F15353",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "smallShop/allMember",
          "style": {
            "navigationBarTitleText": "小店会员",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "live", //根目录下的分包文件路径
      "pages": [
        {
          "path": "info/info",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "packageE", //根目录下的分包文件路径
      "pages": [
        {
          "path": "localLife/brand/brandEntry",
          "style": {
            "navigationBarTitleText": "品牌管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/storeManagement/storeManagement",
          "style": {
            "navigationBarTitleText": "门店管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/verification/verification",
          "style": {
            "navigationBarTitleText": "核销",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/verificationDetail/verificationDetail",
          "style": {
            "navigationBarTitleText": "核销明细",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/saleDetail/saleDetail",
          "style": {
            "navigationBarTitleText": "售卖明细",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/productManage/productManage",
          "style": {
            "navigationBarTitleText": "商品管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/verificationerManage/verificationerManage",
          "style": {
            "navigationBarTitleText": "核销员管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/verifier/verifier",
          "style": {
            "navigationBarTitleText": "核销员",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "localLife/verifierDetail/verifierDetail",
          "style": {
            "navigationBarTitleText": "核销明细",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/miniVideo/myVideo",
          "style": {
            "navigationBarTitleText": "我的视频",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/miniVideo/miniVideo",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#010101"
          }
        },
        {
          "path": "marketingTool/miniVideo/authorVideo",
          "style": {
            "navigationBarTitleText": "作者视频",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/miniVideo/addVideo",
          "style": {
            "navigationBarTitleText": "发布视频",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/miniVideo/goodsVideo",
          "style": {
            "navigationBarTitleText": "商品视频",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/miniVideo/commodityVideo",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#010101"
          }
        },
        {
          "path": "marketingTool/miniVideo/findVideo",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#010101"
          }
        },
        {
          "path": "marketingTool/materialCenter/materialCenter",
          "style": {
            "navigationBarTitleText": "素材中心",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/materialCenter/materialCenterDetail",
          "style": {
            "navigationBarTitleText": "素材中心详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/materialCenter/addMaterial",
          "style": {
            "navigationBarTitleText": "发布素材",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/materialCenter/myMaterial",
          "style": {
            "navigationBarTitleText": "我的素材",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "marketingTool/materialCenter/myMaterialDetail",
          "style": {
            "navigationBarTitleText": "我的素材详情",
            "enablePullDownRefresh": false
          }
        },

        {
          "path": "marketingTool/materialCenter/goodsMaterial",
          "style": {
            "navigationBarTitleText": "商品素材",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "ecCps/opentbRedirectUri",
          "style": {
            "navigationBarTitleText": "登录",
            "enablePullDownRefresh": false
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#f5f5f5"
    //#ifdef H5
    ,"app-plus": {
      "titleNView": false
    }
    //#endif
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
