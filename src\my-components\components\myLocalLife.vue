﻿<template>
  <view>
    <view class="my_order mb-20 b-r-10">
      <view class="order_title font_size14 f-bold mb-20">本地生活</view>
      <u-line></u-line>
      <u-grid :border="false" col="5" class="mb-20">
        <u-grid-item v-if="isBrand !== 2" @click="jumpBrand">
          <view class="iconfont icon-pinpairuzhu iconf-img icon_violet"></view>
          <text class="grid-text font_size12">品牌入驻</text>
        </u-grid-item>
        <u-grid-item v-if="isBrand === 2" @click="jumpBrandPromotion">
          <view class="iconfont icon-pinpairuzhu iconf-img icon_violet"></view>
          <text class="grid-text font_size12">品牌分成</text>
        </u-grid-item>
        <u-grid-item v-if="isUseStore === 1" @click="jumpStore">
          <view class="iconfont icon-mendianguanli iconf-img icon_blue"></view>
          <text class="grid-text font_size12">门店管理</text>
        </u-grid-item>
        <u-grid-item v-if="isUseVerification === 1" @click="jumpVerifier">
          <view class="iconfont icon-hexiaoyuan1 iconf-img icon_blue"></view>
          <text class="grid-text font_size12">核销员</text>
        </u-grid-item>
      </u-grid>
    </view>
  </view>
</template>

<script>
export default {
  name: 'myLocalLife',

  data() {
    return {
      isBrand: 0, // 品牌是否入驻
      isUseStore: 0, // 是否是门店管理
      isUseVerification: 0 // 是否是门店管理
    }
  },
  mounted() {
    this.getLocalLifeUse()
    this.getLocalLifeBrand()
  },

  methods: {
    // 获取品牌判断
    async getLocalLifeBrand() {
      const res = await this.get('/api/localLife/front/brand/verify')
      if (res.code === 0) {
        this.isBrand = res.data.data.status
      }
    },
    // 获取 门店管理·核销员判断 判断
    async getLocalLifeUse() {
      const res = await this.get('/api/localLife/front/getLocalLifeUse')
      if (res.code === 0) {
        this.isUseStore = res.data.isUseStore
        this.isUseVerification = res.data.isUseVerification
      }
    },
    // 品牌入驻
    jumpBrand() {
      this.navTo('/packageE/localLife/brand/brandEntry')
    },
    // 品牌分成
    jumpBrandPromotion() {
      this.navTo(`/pages/promotion/promotion?localLife=${44}`)
      uni.setStorageSync('tabBarActive', 2)
    },
    // 门店管理
    jumpStore() {
      this.navTo('/packageE/localLife/storeManagement/storeManagement')
    },
    // 核销员
    jumpVerifier() {
      this.navTo('/packageE/localLife/verifier/verifier')
    }
  },
}
</script>
<style lang="scss" scoped>
.my_order {
  background-color: #fff;
  padding: 25rpx 30rpx 30rpx 30rpx;
  .iconf-img {
    margin: 34rpx 0 10rpx 0;
    width: 56rpx;
    height: 56rpx;
    display: block;
  }
  .icon_blue {
    font-size: 40rpx;
    color: #3272fd;
  }
  .icon_violet {
    font-size: 40rpx;
    color: #a558ed;
  }
}

@font-face {
  font-family: "iconfont"; /* Project id 432132 */
  src: url('//at.alicdn.com/t/c/font_432132_44anqath052.woff2?t=1730714289533') format('woff2'),
       url('//at.alicdn.com/t/c/font_432132_44anqath052.woff?t=1730714289533') format('woff'),
       url('//at.alicdn.com/t/c/font_432132_44anqath052.ttf?t=1730714289533') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-hexiaoyuan1:before {
  content: "\ecd0";
}

</style>

