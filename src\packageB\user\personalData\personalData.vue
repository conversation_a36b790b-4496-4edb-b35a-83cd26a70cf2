﻿<!-- 个人资料 -->
<template>
    <view>
        <view class="bg-white pt_36 pb_29 d-cc-c m-20 radius10">
            <u--image
                shape="circle"
                :src="list.avatar"
                width="120rpx"
                height="120rpx"
            ></u--image>
            <view class="d-f mt-40">
                <u-upload
                    @afterRead="afterRead"
                    @delete="deletePic"
                    name="1"
                    :maxCount="1"
                    width="150"
                    height="150"
                >
                    <view class="d-cc fs-1 cs1">上传头像</view>
                </u-upload>
                <!-- #ifdef MP-WEIXIN -->
                <view>
                    <button
                        class="d-cc fs-1 ml-20 cs2"
                        open-type="chooseAvatar"
                        @chooseavatar="onChooseAvatar"
                    >
                        获取微信头像
                    </button>
                </view>
                <!-- #endif -->
            </view>
        </view>
        <!-- 用户信息表单 -->
        <view class="m-20 p-20 bg-white radius10">
            <view class="d-bf">
                <view>用户名</view>
                <input
                    placeholder="用户名"
                    placeholder-class="f12"
                    v-model="list.username"
                    class="input-css"
                    disabled
                />
                <view class="red" @click="changeUsername">{{ list.username ? '切换手机号' : '绑定手机号' }}</view>
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf">
                <view>用户昵称</view>
                <input
                    placeholder="用户昵称"
                    type="nickname"
                    placeholder-class="f12"
                    v-model="list.nickname"
                    class="input-css"
                    @change="onChangeNickName"
                />
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf">
                <view>身份证</view>
                <input
                    placeholder="身份证"
                    placeholder-class="f12"
                    v-model="list.id_card"
                    class="input-css"
                    @blur="onBlurIDcard"
                />
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf">
                <view>支付宝账号</view>
                <input
                    placeholder="支付宝账号"
                    placeholder-class="f12"
                    v-model="list.ali_account"
                    class="input-css"
                />
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf">
                <view>姓名</view>
                <input
                    placeholder="姓名"
                    placeholder-class="f12"
                    v-model="list.full_name"
                    class="input-css"
                />
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf" @click="jumpAgreement('signIn')">
                <view>注册协议</view>
                <u-icon name="arrow-right" size="28"></u-icon>
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf" @click="jumpAgreement('user')">
                <view>用户服务协议</view>
                <u-icon name="arrow-right" size="28"></u-icon>
            </view>
            <view
                style="width: 658rpx; height: 1rpx; background-color: #eeeeee"
                class="d-cc mt-40 mb_40"
            ></view>
            <view class="d-bf" @click="jumpAgreement('privacy')">
                <view>隐私协议</view>
                <u-icon name="arrow-right" size="28"></u-icon>
            </view>
            <!-- <view style="width: 658rpx;height: 1rpx;background-color: #eeeeee;" class="d-cc mt-40"></view>
			<view class="d-bf mt-40">
				<view>微信号</view>
				<input placeholder="请输入微信号" v-model="list.wx_username"  placeholder-class="f12" class="input-css"/>
			</view>
			<view class="mt-80 mb-10">
				<view>微信二维码</view>
				<view class="mt_22">
					<u-upload 
						:fileList="fileList2" 
						@afterRead="afterRead($event,'qrCode')" 
						@delete="deletePic"
						accept="image"
						name="2"
						:previewFullImage="true"
						:maxCount="1" 
						width="150" 
						height="150">
						<u--image :src="list.qr_code"  width="150rpx" height="150rpx"></u--image>
					</u-upload>
					
				</view>
			</view> -->
        </view>
        <!-- 确认按钮 -->
        <view class="d-cc-c mt_79">
            <view class="d-cc btn" @click="updateProfile">确认修改</view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <my-privacy-auth-popup />
        <!-- #endif -->
        <!-- 更换手机号模态框 -->
        <view>
            <u-modal 
              :show="show"
              showCancelButton
              :scrollable="false"
              @confirm="changeUsernameMobile"
              @cancel="show = false"
            >
                <u--form
                    labelPosition="left"
                    :model="bindingAccount"
                    :rules="miniRules"
                    ref="miniRegosterForm"
                >
                    <u-form-item prop="userName" borderBottom>
                    <u--input
                        v-model="bindingAccount.userName"
                        placeholder="请输入您的手机号"
                        border="none"
                        clearable
                    ></u--input>
                    </u-form-item>
                    <u-form-item prop="captcha_code" borderBottom>
                    <view class="d-f mt-20" style="width: 550rpx">
                        <u--input
                            v-model="bindingAccount.captcha_code"
                            placeholder="请输入验证码"
                            border="none"
                            @input="handleCode"
                        ></u--input>
                        <button
                            class="smallUsercode"
                            @click="$clicks(miniSendCode)"
                            :disabled="isDisable"
                            >
                            {{ miniCodeText }}
                        </button>
                    </view>
                    </u-form-item>
                </u--form>
            </u-modal>
        </view>
    </view>
</template>

<script>
export default {
        data() {
        return {
            list: {}, //个人资料信息
            fileList1: [],
            fileList2: [],
            isUpdate: false,
            show: false, // 控制模态框
            bindingAccount: {
                userName: '',
                captcha_code: '',
            },
            isDisable: false,
            miniIsDisable: false,
            miniCodeText: '获取短信验证码',
            miniRules: {
                userName: [
                {
                    required: true,
                    message: '请输入手机号',
                    trigger: ['blur', 'change'],
                },
                {
                    // 自定义验证函数，见上说明
                    validator: (rule, value, callback) => {
                    // 上面有说，返回true表示校验通过，返回false表示不通过
                    // uni.$u.test.mobile()就是返回true或者false的
                    this.miniUserShow = uni.$u.test.mobile(value)
                    return uni.$u.test.mobile(value)
                    },
                    message: '手机号码不正确',
                    // 触发器可以同时用blur和change
                    trigger: ['blur'],
                },
                ],
            },
        }
    },
    onLoad() {
        this.obtainingUserInformation() //获取个人资料
    },
    methods: {
        /**
         * 跳转协议
         * @param {Object} type  signIn=注册协议 user=用户服务协议 privacy=隐私协议
         */
        jumpAgreement(type) {
            uni.navigateTo({
                url: `/packageD/agreement/agreement?type=${type}`,
            })
        },
        // 删除图片
        deletePic(event) {
            this[`fileList${event.name}`].splice(event.index, 1)
            this.$set(this.list, 'qr_code', '')
        },
        // 新增图片
        async afterRead(event, name) {
            // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
            let lists = [].concat(event.file)
            let fileListLen = this[`fileList${event.name}`].length

            lists.map(item => {
                this[`fileList${event.name}`].push({
                    ...item,
                    status: 'uploading',
                    message: '上传中',
                })
            })
            for (let i = 0; i < lists.length; i++) {
                var result
                if (name == 'qrCode') {
                    result = await this.uploadFilePromise(
                        lists[i].url,
                        'qrCode',
                    )
                } else {
                    this.loading('上传中')
                    result = await this.uploadFilePromise(lists[i].url)
                    uni.hideLoading()
                }
                let item = this[`fileList${event.name}`][fileListLen]
                this[`fileList${event.name}`].splice(
                    fileListLen,
                    1,
                    Object.assign(item, {
                        status: 'success',
                        message: '',
                        url: result,
                    }),
                )
                fileListLen++
            }
            this.isUpdate = false
        },
        uploadFilePromise(url, name) {
            return new Promise((resolve, reject) => {
                let a = uni.uploadFile({
                    url: this.api.host + '/api/common/upload',
                    filePath: url,
                    name: 'file',
                    formData: {
                        file: url,
                    },
                    success: res => {
                        console.log(res)
                        setTimeout(() => {
                            let json = JSON.parse(res.data)
                            if (name == 'qrCode') {
                                //二维码上传
                                this.$set(
                                    this.list,
                                    'qr_code',
                                    json.data.file.url,
                                )
                                console.log(this.list)
                            } else {
                                //头像上传
                                this.$set(
                                    this.list,
                                    'avatar',
                                    json.data.file.url,
                                )
                            }
                            resolve(url)
                        }, 1000)
                    },
                })
            })
        },
        // 处理验证码
        handleCode(){
            if(this.bindingAccount.captcha_code.length > 4){
            this.bindingAccount.captcha_code.slice(0,4)
            }
            /* if(this.form.captcha_code.length > 4){
            this.form.captcha_code.slice(0,4)
            } */
        },
        obtainingUserInformation() {
            //获取个人资料
            this.post('/api/center/getProfile', {}, true).then(data => {
                this.list = data.data
            })
        },
        onChooseAvatar(e) {
            const { avatarUrl } = e.detail
            this.$set(this.list, 'avatar', avatarUrl)
            this.isUpdate = true
        },
        onChangeNickName(e) {
            if (e.detail) {
                this.$set(this.list, 'nickname', e.detail.value)
            }
        },
        onBlurIDcard(e) {
            if (e.detail.value.length !== 18) {
                uni.showToast({
                    title: '请正确输入18位身份证号',
                    icon: 'none',
                    duration: 1000,
                })
            }
        },
        // wechatUser() { //获取微信用户信息
        // 	let that = this
        // 	// 获取微信用户信息
        // 	uni.getUserProfile({
        // 		desc: '修改用户资料',
        // 		success: function(res) {
        // 			let json = JSON.parse(res.rawData)
        // 			that.$set(that.list, "avatar", json.avatarUrl)
        // 			that.$set(that.list, "nickname", json.nickName)
        // 			that.showSuccess("获取成功")
        // 		}
        // 	});
        // },
        // 修改手机号
        changeUsername() {
            this.show = true
            this.bindingAccount = {}
        },
        miniSendCode() {
            this.post('/api/user/sendCode', {
                mobile: this.bindingAccount.userName,
                type: 'updateUserName',
            })
                .then(res => {
                if (res.code === 0) {
                    let data = res.data
                    this.bindingAccount.captcha_key = data.captcha_key
                    this.miniTimeReduce()
                }
                })
                .catch(Error => {
                console.log(Error)
                })
        },
        miniTimeReduce() {
            let _this = this
            let time = 60
            this.timer = setInterval(() => {
                this.miniCodeText = `重新发送${time}s`
                time -= 1
                this.miniIsDisable = true
                if (time <= 0) {
                this.miniIsDisable = false
                this.miniCodeText = '获取短信验证码'
                clearInterval(_this.timer)
                }
            }, 1000)
        },
        // 确认修改手机号
        async changeUsernameMobile() {
            const data = {
                username: this.bindingAccount.userName,
                captcha_key: this.bindingAccount.captcha_key,
                captcha_code: this.bindingAccount.captcha_code
            }
            const res = await this.post('/api/user/changeUserNameByCode',data)
            if (res.code === 0) {
                uni.showToast({
                    title: '修改成功',
                    icon: 'none',
                })
                this.show = false
                this.list.username = this.bindingAccount.userName
                // this.obtainingUserInformation()
            }
        },
        async updateProfile() {
            //修改个人资料
            // 修改个人资料
            if (this.isUpdate) {
                await this.uploadFilePromise(this.list.avatar)
            }

            this.post('/api/center/updateProfile', this.list, true).then(
                data => {
                    setTimeout(function () {
                        uni.navigateBack({
                            url: '../../membercenter/membercenter',
                        })
                    }, 1500)
                    this.showSuccess('修改成功')
                },
            )
        },
    },
}
</script>

<style scoped>
.cs1 {
    width: 260rpx;
    height: 60rpx;
    background-color: #f14e4e;
    border-radius: 10rpx;
    color: #ffffff;
}

.cs2 {
    width: 260rpx;
    height: 60rpx;
    background-color: #f87c17;
    border-radius: 10rpx;
    color: #ffffff;
}

.btn {
    width: 650rpx;
    height: 70rpx;
    background-color: #f14e4e;
    border-radius: 6rpx;
    color: #ffffff;
}

.uni-input-input,
.uni-input-placeholder {
    color: #a5a5a5;
}

/deep/.f12 {
    font-size: 15px;
}

.input-css {
    text-align: right;
    font-size: 30rpx;
}
.red {
    color: #f15353;
}
.smallUsercode {
    width: 210rpx;
    font-size: 22rpx;
    color: #f15353;
}
</style>

