﻿<!-- 购物车页面 -->
<template>
  <view>
    <view v-if="!checkNull(loginUser)">
      <my-unlogged-page></my-unlogged-page>
    </view>
    <view class="shopping_page" v-else>
      <header-bar
        :isBack="true"
        :fixed="true"
        title="购物车"
        titleTintColor="#333"
        :bgColor="{ 'background-color': '#fff' }"
      >
        <text slot="back" class="iconfont icon-member-left"></text>
        <text slot="string" @click="shopEdit">
          {{ edit === true ? '完成' : '编辑' }}
        </text>
      </header-bar>
      <view class="mb_90"></view>
      <view class="shoplist-main" v-if="!this.$isEmpty.isEmpty(this.shopList)">
        <block v-for="(item, index) in shopList" :key="item.id">
          <view class="shopping_list mb-20 b-r-10">
            <view class="shopping_title d-cf">
              <u-checkbox-group
                placement="column"
                v-if="item.is_expired !== 1 || checkedShow"
              >
                <u-checkbox
                  shape="circle"
                  size="30"
                  activeColor="#f14e4e"
                  :checked="item.checked === 0 ? false : true"
                  @change="shoppingChange(index, item)"
                  :name="item.id"
                  :disabled="item.is_expired === 1 && !edit"
                ></u-checkbox>
              </u-checkbox-group>
              <image
                class="shop_logo"
                :src="item.supplier && item.supplier.shop_logo"
              ></image>
              <view class="font_size16">
                {{ item.supplier && item.supplier.name }}
              </view>
            </view>
            <!-- 未失效显示 -->
            <view v-if="item.is_expired === 0" class="shopping_box d-f">
              <view class="shop_box_left d-cf">
                <image
                  :src="item.product && item.product.image_url"
                  @click="
                    navTo(
                      '/packageA/commodity/commodity_details/commodity_details?id=' +
                        item.product.id,
                    )
                  "
                ></image>
              </view>
              <view class="shop_box_right">
                <u--text
                  size="14"
                  color="#333"
                  lineHeight="36rpx"
                  :lines="2"
                  :text="item.product && item.product.title"
                  customStyle="width:418rpx;font-size:28rpx"
                  @click="
                    navTo(
                      '/packageA/commodity/commodity_details/commodity_details?id=' +
                        item.product.id,
                    )
                  "
                ></u--text>
                <view class="d-cf mt-20 shop_standard">
                  <text class="font_size12">
                    {{ item.sku && item.sku.title }}
                  </text>
                </view>

                <view class="d-bc mt-20 calculate">
                  <view class="font_size16 c-f1">
                    ￥{{ (item.sku && item.sku.price) || 0.0 }}
                  </view>
                  <view>
                    <!--	:isMax="item.product.skus[0].stock" 记得放回去 -->
                    <u-number-box
                      :value="item.qty"
                      :min="1"
                      :max="999"
                      @change="numberChange($event, item.id)"
                      :index="index"
                      :isMin="item.qty === 1"
                      :disabled="item.is_expired === 1"
                    ></u-number-box>
                  </view>
                </view>
              </view>
            </view>
            <!-- 失效显示 -->
            <view v-if="item.is_expired === 1" class="shopping_box d-f">
              <view class="shop_box_expired">失效</view>
              <view class="shop_box_left d-cf">
                <image
                  :src="item.product && item.product.image_url"
                  @click="
                    navTo(
                      '/packageA/commodity/commodity_details/commodity_details?id=' +
                        item.product.id,
                    )
                  "
                ></image>
              </view>
              <view class="shop_box_right">
                <u--text
                  size="14"
                  color="#999999"
                  lineHeight="36rpx"
                  :lines="2"
                  :text="item.product && item.product.title"
                  customStyle="width:418rpx;font-size:28rpx"
                  @click="
                    navTo(
                      '/packageA/commodity/commodity_details/commodity_details?id=' +
                        item.product.id,
                    )
                  "
                ></u--text>
                <view class="d-cf mt-20 shop_standard">
                  <text class="font_size12" style="color: #999999">
                    {{ item.sku && item.sku.title }}
                  </text>
                  <!-- <view class="iconfont icon-member-bottom"></view> -->
                </view>

                <view class="d-bc mt-20 calculate">
                  <view class="font_size16 c-f1" style="color: #999999">
                    ￥{{ (item.sku && item.sku.price) || 0.0 }}
                  </view>
                  <view>
                    <!--	:isMax="item.product.skus[0].stock" 记得放回去 -->
                    <u-number-box
                      :value="item.qty"
                      :min="1"
                      color="#999999"
                      :max="999"
                      @change="numberChange($event, item.id)"
                      :index="index"
                      :isMin="item.qty === 1"
                      :disabled="item.is_expired === 1"
                    ></u-number-box>
                  </view>
                </view>
              </view>
            </view>
            <view class="mb_20 f fac fjsb" v-if="item.is_expired === 1">
              <text style="width: 500rpx">
                失效原因: {{ item.expired_message }}
              </text>
              <u-button
                text="重新选择"
                shape="circle"
                style="width: 140rpx; height: 56rpx"
                @click="resetChoose(index, item)"
              ></u-button>
            </view>
            <u-line length="95%"></u-line>
          </view>
        </block>
      </view>
      <view v-else>
        <u-empty
          mode="car"
          width="300rpx"
          height="300rpx"
          icon="http://cdn.uviewui.com/uview/empty/car.png"
        ></u-empty>
        <view class="card_no_menu">
          <view
            class="card_no_nav"
            @click="tabTo('/pages/membercenter/membercenter')"
          >
            个人中心
          </view>
          <view class="card_no_nav togo" @click="tabTo('/pages/index/index')">
            去逛逛
          </view>
        </view>
      </view>

      <u-checkbox-group v-model="accountCheck" placement="column">
        <view class="account_close d-bf">
          <view class="d-cf">
            <u-checkbox
              shape="circle"
              size="30"
              activeColor="#f14e4e"
              :checked="allFlag === 0 ? false : true"
              @change="checkAll"
            ></u-checkbox>
            <view class="f-regular c-20 checkAll font_size12">全选</view>
            <view class="font_size13">
              (不含运费）合计：
              <text class="c-f1">{{ allprice }}</text>
            </view>
          </view>

          <view class="account_btn f-regular c-white" @click="accountOut">
            {{ edit === true ? '删除' : '结算' }}
          </view>
        </view>
      </u-checkbox-group>
    </view>
    <my-tabbar ref="myTabBar"></my-tabbar>
  </view>
</template>

<script>
// #ifdef H5
import myUnloggedPage from '@/my-components/components/myUnloggedPage.vue'
import myTabbar from '@/my-components/components/myTabbar.vue'
// #endif

export default {
  // #ifdef H5
  components: {
    'my-unlogged-page': myUnloggedPage,
    'my-tabbar': myTabbar
  },
  // #endif
    data() {
    return {
      shoppingId: [],
      accountCheck: [],
      shopList: [],
      ids: [],
      allFlag: 0, //全选
      edit: false,
      failureList: [],
      redirectPageUrl: '',
      value: 0,
      loginUser: '',
      checkedShow: false, // 编辑显示按钮
    }
  },
  onLoad() {},
  onShow() {
    setTimeout(() => {
      this.$refs.myTabBar.tabBarActive = uni.getStorageSync('tabBarActive')
    }, 100)
    this.redirectPageUrl = uni.getStorageSync('redirect_page')
    this.loginUser = uni.getStorageSync('user')
    if ((this.redirectPageUrl ?? '') === '') {
      this.redirectPageUrl = ''
      this.shoppingList()
    } else {
      this.getBatchOrderCarts()
    }
  },
  computed: {
    // 计算总价
    allprice() {
      let price = 0
      if (this.shopList !== null) {
        this.shopList.forEach(item => {
          if (item.checked) {
            price += item.sku.price * item.qty
          }
        })
      }
      return '￥' + this.toDecimal2(price)
    },
  },
  methods: {
    shoppingList() {
      this.allFlag === true ? (this.allFlag = 1) : (this.allFlag = 0)
      this.post('/api/shoppingcart/list', { checked: this.allFlag }, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.shopList = data.shopping_carts
            this.reducedPrice(this.shopList)
            this.shopAll()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getBatchOrderCarts() {
      this.get('/api/shoppingcart/getBatchOrderCarts', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.shopList = data.shopping_carts
            this.reducedPrice(this.shopList)
            this.shopAll()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    shopEdit() {
      this.edit = !this.edit
      if (!this.edit) {
        this.checkedShow = false
        this.shoppingList()
      } else {
        this.checkedShow = true
      }
    },
    checkAll() {
      //全选功能
      this.allFlag = !this.allFlag
      for (let i in this.shopList) {
        //选中修改单选的状态
        this.shopList[i].checked = this.allFlag
      }
      if (!this.edit) {
        this.batch()
      }
    },
    batch() {
      //监听是否全部选中选中
      let urlApi = ''
      if (this.redirectPageUrl === '/packageC/member/bulkOrder') {
        urlApi = '/api/shoppingcart/updateBatchCart/batch'
      } else {
        urlApi = '/api/shoppingcart/update/batch'
      }
      this.allFlag == true ? (this.allFlag = 1) : (this.allFlag = 0) //必须要经过这一步改变值
      this.post(urlApi, { checked: this.allFlag }, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.shopList = data.shopping_carts
            this.reducedPrice(this.shopList)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    reducedPrice(shopList) {
      //价格处理
      for (let i in shopList) {
        shopList[i].sku.price = this.toYuan(shopList[i].sku.price)
      }
    },
    shopAll() {
      //是否全部选中
      let length2 = 0
      if (this.shopList !== null) {
        this.shopList.forEach(item => {
          if (item.checked) {
            length2++
          }
        })
        if (this.shopList.length === length2) {
          this.allFlag = 1
        } else {
          this.allFlag = 0
        }
      }
    },
    shoppingChange(index, item) {
      //是否选中
      let checked = this.shopList[index].checked
      this.shopList[index].checked = !item.checked
      item.checked === true ? (item.checked = 1) : (item.checked = 0)
      let urlApi = ''
      if (this.redirectPageUrl === '/packageC/member/bulkOrder') {
        urlApi = '/api/shoppingcart/updateBatchCart'
      } else {
        urlApi = '/api/shoppingcart/update'
      }
      if (!this.checkedShow) {
        this.post(urlApi, { checked: item.checked, id: item.id }, true)
          .then(res => {
            if (res.code === 0) {
              let data = res.data
              this.shopList = data.shopping_carts
              this.reducedPrice(this.shopList) //计算价格用的
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {
            console.log(Error)
          })
      }
      this.shopAll()
    },
    childrenChange(index, goodsIndex) {
      //单商品勾选
      let length = 0
      let length2 = 0
      this.shopList[index].shopListChildren[goodsIndex].checked =
        !this.shopList[index].shopListChildren[goodsIndex].checked
      this.shopList[index].shopListChildren.forEach(item => {
        if (item.checked) {
          length++
        }
      })
      if (this.shopList[index].shopListChildren.length == length) {
        this.shopList[index].checked = true
      } else {
        this.shopList[index].checked = false
      }

      this.shopList.forEach(item => {
        if (item.checked) {
          length2++
        }
      })
      if (this.shopList.length === length2) {
        this.allFlag = true
      } else {
        this.allFlag = false
      }
    },
    // 重新选择
    resetChoose(index, item) {
      this.shoppingChange(index, item)
    },
    accountOut() {
      if (!this.$isEmpty.isEmpty(this.shopList)) {
        let shopArr = this.shopList.filter(item => {
          return item.checked === 1
        })
        let shoppingIds = []
        for (let item of shopArr) {
          shoppingIds.push(item.id)
        }
        this.ids = [...new Set(shoppingIds)] //去重
      }
      if (this.edit && this.ids.length > 0) {
        let urlApi = ''
        if (this.redirectPageUrl === '/packageC/member/bulkOrder') {
          urlApi = '/api/shoppingcart/deleteBatchCart/batch'
        } else {
          urlApi = '/api/shoppingcart/delete/batch'
        }
        this.post(urlApi, { ids: this.ids }, true)
          .then(res => {
            if (res.code === 0) {
              let data = res.data
              this.shopList = data.shopping_carts
              this.reducedPrice(this.shopList)
              this.toast('删除成功')
              this.ids = []
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {
            console.log(Error)
          })
      } else {
        if (this.ids.length > 0) {
          setTimeout(() => {
            uni.navigateTo({
              url: '/packageA/goodsorder/goodsorder',
            })
          }, 1000)
        } else {
          this.toast('你还没有选中商品')
        }
      }
    },
    numberChange($event, id) {
      //修改输入数量
      let urlApi = ''
      if (this.redirectPageUrl === '/packageC/member/bulkOrder') {
        urlApi = '/api/shoppingcart/updateBatchCart'
      } else {
        urlApi = '/api/shoppingcart/update'
      }
      this.post(urlApi, { id, qty: $event.value }, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.shopList = data.shopping_carts
            this.reducedPrice(this.shopList)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>
<style scoped>
/*#ifdef H5 */
.uni_topbar {
  position: fixed;
  z-index: 99;
  width: 100%;
  top: 0rpx;
}
/*#endif*/
.u-number-box ::v-deep .u-number-box__minus {
  height: 42rpx !important;
}
.u-number-box ::v-deep .u-number-box__plus {
  height: 42rpx !important;
}
.u-number-box__plus ::v-deep .u-number-box__minus {
  height: 42rpx !important;
}
.u-number-box ::v-deep .u-number-box__input {
  width: 60rpx !important;
  width: auto;
  height: 42rpx !important;
  background-color: #f2f3f5 !important;
  border-radius: 4rpx !important;
}
</style>
<style lang="scss" scoped>
/*#ifdef MP-WEIXIN*/
.calculate ::v-deep .u-number-box .u-number-box__plus {
  height: 42rpx !important;
}
/*#endif*/
/*#ifdef MP-WEIXIN*/
.calculate ::v-deep .u-number-box .u-number-box__minus {
  height: 42rpx !important;
}
/*#endif*/
/*#ifdef MP-WEIXIN*/
.calculate ::v-deep .u-number-box .u-number-box__input {
  width: 60rpx !important;
  width: auto;
  height: 42rpx !important;
  background-color: #f2f3f5 !important;
  border-radius: 4rpx !important;
}
/*#endif*/
.shopping_page {
  .shoplist-main {
    padding: 20rpx 20rpx 133rpx 20rpx;
    .shopping_list {
      box-sizing: border-box;
      background-color: #fff;
      padding: 30rpx 0 0 20rpx;
      .shopping_title {
        margin-bottom: 6rpx;
        .shop_logo {
          width: 50rpx;
          height: 50rpx;
          margin-right: 10rpx;
        }
        .iconfont {
          font-size: 40rpx;
          color: #f14e4e;
          margin: 0 14rpx 0 12rpx;
        }
      }
      .shopping_box {
        margin-top: 30rpx;
        padding-bottom: 30rpx;
        .shop_box_expired {
          width: 100rpx;
          background-color: #f3f1f1;
          height: 40rpx;
          margin-right: 20rpx;
          margin-top: 80rpx;
          text-align: center;
          padding: 0rpx 5rpx;
          border-radius: 20rpx;
        }
        .shop_box_left {
          margin-right: 20rpx;
          image {
            width: 140rpx;
            height: 140rpx;
            background-color: #666666;
            border-radius: 8rpx;
          }
        }
        .shop_box_right {
          width: 100%;
          .shop_standard {
            width: 317rpx;
            height: 46rpx;
            padding: 0 0 0 6rpx;
            // padding: 12rpx 18rpx 12rpx 20rpx;
            background-color: #f9f9f9;
            border-radius: 8px;
          }
        }
        .calculate {
          padding: 0 30rpx 0 0;
        }
      }
    }
  }
  .card_no_menu {
    width: 100%;
    height: 80rpx;
    margin-top: 180rpx;
    .card_no_nav {
      height: 72rpx;
      width: 43%;
      background: #eee;
      margin: 0 3%;
      float: left;
      border: 2rpx solid #f15353;
      border-radius: 0.3125rem;
      text-align: center;
      line-height: 72rpx;
      color: #f15353;
    }
    .togo {
      color: #fff;
      background: #f15353;
    }
  }
  .account_close {
    width: 100%;
    height: 113rpx;
    box-sizing: border-box;
    padding: 0 30rpx;
    position: fixed;
    z-index: 2;
    bottom: calc(98rpx + env(safe-area-inset-bottom));  //获取底部安全区域 苹果因安全区域。底部结算盒子遮挡tabbar
    left: 0;
    background-color: #ffffff;
    box-shadow: 0px -2px 7px 0px rgba(173, 173, 173, 0.31);
    .checkAll {
      //修改公共样式的时候，记得修改样式名字，会有样式不生效
      margin: 0 33rpx 0 14rpx;
    }
    .account_btn {
      background-color: #f14e4e;
      border-radius: 30px;
      padding: 18rpx 40rpx 18rpx 40rpx;
    }
  }
}
</style>

