﻿<!-- 批量下单下载上传 -->
<template>
  <view>
    <view v-if="batchOrdersList.length === 0">
      <view class="download">
        <view class="title d-cc">
          <text class="iconfont icon-fontclass-xiazai"></text>
          <text class="font_size13 c-666">下载批量下单表格</text>
        </view>
        <view class="d-c mt_80">
          <view class="download-btn d-cc download-bg" @click="uploadBulk()">
            下载批量下单表格模板
          </view>
        </view>
      </view>

      <view class="download">
        <view class="title upload-title d-cc">
          <text class="iconfont icon-fontclass-shangchuan"></text>
          <text class="font_size13 c-666">
            填写完信息后上传表格，上传符合模板的Excel文件后，可为您批量下单
          </text>
        </view>
        <!-- 上传的文件列表展示 -->
        <view class="d-c mt_38">
          <view class="download-btn d-cc upload-bg" ref="input">上传文件</view>
        </view>
      </view>

      <view class="down-hint">
        <text class="c-66">
          注：只支持上传小于3M，.xls .xlsx .csv格式的文件
        </text>
        <view class="c-66 text">
          <text class="c-orange">最多只支持500笔订单</text>
          ，导入表格前请认真检查相关的信息，
          确保收货人、手机号、购买数量等信息正确无误。
        </view>
        <view class="c-orange text">
          H5批量下单只支持一个地址，以第一个商品的地址为下单地址
        </view>
      </view>
    </view>
    <view class="batch-orders" v-else>
      <view class="batch-head d-f-c">
        <view class="d-c succeed">
          <view class="iconfont icon-all_select_active"></view>
        </view>
        <view class="batch-hint">
          表格导入成功，请核对您的信息，确认无误后点击提交 订单即可完成批量下单
        </view>
        <view class="remark d-f">
          <view class="iconfont icon-fontclass-gantanhao"></view>
          <view>备注：红色字体显示的是错误的原因，您可编辑按钮进行修改</view>
        </view>
      </view>

      <view class="orders-list">
        <block v-for="(item, index) in batchOrdersList" :key="index">
          <view class="orders-item">
            <view class="title d-bf">
              <view class="orders-title-left">
                <text class="serial">序号{{ index + 1 }}</text>
                <text :class="item.purchase_sn_err ? 'c-orange' : ''">
                  下单号：{{ item.purchase_sn }}
                </text>
              </view>
              <view class="orders-title-right d-bf2">
                <text
                  class="iconfont icon-fontclass-qianshu"
                  @click="edit(item)"
                ></text>
                <text
                  class="iconfont icon-appointment_delete"
                  @click="bulkDel(item.id)"
                ></text>
              </view>
            </view>
            <u-line></u-line>
            <view class="orders-main">
              <view class="order-attribute">商品：{{ item.product_name }}</view>
              <view class="order-attribute d-cf">
                <view class="mr-100">第一规格：{{ item.sku_name }}</view>
                <view>第二规格：{{ item.sku2_name }}</view>
              </view>
              <view
                class="order-attribute"
                :class="item.qty_err ? 'c-orange' : ''"
              >
                购买数量：{{ item.qty }}
              </view>
              <view class="order-attribute d-cf">
                <view
                  class="mr-70"
                  :class="item.realname_err ? 'c-orange' : ''"
                >
                  收货人姓名：{{ item.realname }}
                </view>
                <view :class="item.mobile_err ? 'c-orange' : ''">
                  收货手机号：{{ item.mobile }}
                </view>
              </view>
              <view
                class="order-attribute"
                :class="
                  item.province_err || item.city_err || item.county_err
                    ? 'c-orange'
                    : ''
                "
              >
                所在地区：{{ item.province }}-{{ item.city }}-{{ item.county }}
              </view>
              <view :class="item.detail_err ? 'c-orange' : ''">
                收货人详细地址：{{ item.detail }}
              </view>
            </view>
          </view>
        </block>
      </view>

      <view class="mb100"></view>
      <view class="mb100"></view>
      <view class="mb100"></view>
    </view>

    <bulk-popup
      :bulkForm="bulkForm"
      :address="addressName"
      :bulkShow="bulkShow"
      :addressShow="addressShow"
      @bulksClose="bulkClose"
      @bulksConfirm="bulkConfirm"
      @addressBtnOn="addressShowOn"
    ></bulk-popup>
    <bulk-del-popup
      :bulkDelShow="bulkDelShow"
      :bulkDelId="bulkDelId"
      @bulkDelCancel="bulkDelCancel"
      @bulkDelConfirm="bulkDelConfirm"
    ></bulk-del-popup>

    <!-- 选择地址的UI组件 -->
    <address-popup
      v-if="bulkShow"
      :addressShow="addressShow"
      @_closeDateLw="closeDateLw"
      @AddressSetOn="addressSetOn"
    ></address-popup>
    <!-- 底部显示数据 -->
    <view class="bulk-footer" v-if="batchOrdersList.length !== 0">
      <view class="distribution d-bf">
        <view class="manner d-cf">
          <text>配送方式:</text>
          <u-radio-group placement="row" v-model="bulkType">
            <u-radio
              :customStyle="{ marginRight: '10rpx' }"
              v-for="(item, index) in distribution"
              :key="index"
              :label="item.name"
              :name="item.type"
              @change="bulkChange(item.type)"
              activeColor="#f14e4e"
              size="24rpx"
            ></u-radio>
          </u-radio-group>
        </view>
      </view>
      <view class="total-list">
        共{{ batchOrdersList.length }}条数据，其中{{ successCount }}条正确，{{
          failCount
        }}条有错误
      </view>
      <view class="bulk-group d-cc">
        <view class="bulk-btn" @click="bulkBatchDel">一键删除错误数据</view>
        <view class="bulk-btn red-btn" @click="bulkBtn">提交订单</view>
      </view>

      <view class="bulk-order-msg">只提交数据正确的订单</view>
    </view>
  </view>
</template>

<script>
export default {
    data() {
    return {
      bulkShow: false,
      bulkDelShow: false,
      addressShow: false,
      fileList: [],
      bulkDelId: 0, // 删除ID
      batchOrdersList: [],
      bulkForm: {},
      addressName: '',
      distribution: [
        {
          name: '快递',
          type: 1,
        },
        {
          name: '门店自提',
          type: 2,
        },
      ],
      shipping_method_id: 1,
      bulkType: 1,
      fileUrl: '',
      failCount: 0, // 失败条数
      successCount: 0, // 成功条数
      mimeMap: {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    }
  },
  onReady() {
    if (this.batchOrdersList.length > 0) {
      // 编辑的时候兼容小程序
      this.$refs.bulkForm.setRules(this.rules)
    }
  },
  mounted() {
    // #ifdef H5
    this.$nextTick(function () {
      const input = document.createElement('input')
      input.style.width = '100%'
      input.type = 'file' // 添加file类型
      input.accept = '.xlsx' // 限制只能上传PDF文件，可以不限制，能上传任何类型
      input.style.height = '32px'
      input.style.position = 'absolute'
      input.style.top = '484rpx'
      input.style.right = '0'
      input.style.opacity = '0'
      input.style.overflow = 'hidden' // 防止注意input 元素溢出
      input.id = 'file'
      const _this = this
      setTimeout(() => {
        this.$refs.input.$el.appendChild(input) // 这里可能会报$el找不到错误，所以加了延时器，报错的原因是this.$refs.input没有找到为null，所以需要等页面结构加载完后再将其添加进去
        input.onchange = event => {
          // 点击上传选择文件
          const file = event.target.files
          if (file.length > 0) {
            for (let i = 0; i < file.length; i++) {
              _this.uploadAPI(file[i])
            }

            // file.map(item => { // 因为后台限制，只能一个一个文件的上传，所以当选择多个文件后，需要遍历一个一个调用上传接口
            // 	_this.uploadAPI(item); // 上传图片
            // })
          }
        }
      }, 1000)
    })
    // #endif
  },
  methods: {
    // #ifdef H5
    saveAs(data, name) {
      const urlObject = window.URL || window.webkitURL || window
      const blobUrl = new Blob([data], { type: this.mimeMap.xlsx })
      const linkBtn = document.createElement('a')
      linkBtn.href = urlObject.createObjectURL(blobUrl)
      linkBtn.setAttribute('download', 'xlsx')
      linkBtn.download = name
      linkBtn.click()
      uni.hideLoading()
    },
    // #endif
    uploadBulk() {
      // #ifdef H5
      const file = this.api.host + '/api/common/downloadBatchOrder'
      console.log(file)
      // 判断是否在微信
      // let ua = navigator.userAgent.toLowerCase();
      // let isWeixin = ua.indexOf('micromessenger') != -1;
      // //判断是否是钉钉
      // let dingding = ua.indexOf('dingtalk') != -1;
      // if (isWeixin || dingding) {
      // 	// alert('在微信')
      // 	uni.showToast({
      // 		title:"请使用其他浏览器打开",
      // 		icon: "none",
      // 		duration:2000
      // 	})
      // } else {
      const that = this
      const url = file
      const fileName = 'batch_order_tmp' // 这里的filename是有后缀名的，没有后缀名的自己拼接一下
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.setRequestHeader('x-token', uni.getStorageSync('token'))
      // 为了避免大文件影响用户体验，建议加loading
      xhr.onload = function () {
        uni.showLoading({
          title: '加载中',
        })
        if (xhr.status === 200) {
          // 获取文件blob数据并保存
          that.saveAs(xhr.response, fileName)
        } else {
          uni.hideLoading()
          uni.showToast({
            title: '下载文件失败',
            icon: 'none',
            duration: 2000,
          })
        }
      }
      xhr.send()
      // }
      // #endif

      // #ifndef H5
      uni.downloadFile({
        url: this.api.host + '/api/common/downloadBatchOrder', // 下载地址接口返回
        header: {
          'x-token': uni.getStorageSync('token'),
        },
        success: data => {
          console.log(data)
          uni.showLoading({
            title: '加载中',
          })
          if (data.statusCode === 200) {
            // 文件保存到本地
            uni.hideLoading()
            uni.saveFile({
              tempFilePath: data.tempFilePath, // 临时路径
              success: function (res) {
                console.log(res)
                uni.showToast({
                  icon: 'none',
                  mask: true,
                  title: '文件已保存：' + res.savedFilePath, // 保存路径
                  duration: 3000,
                })
                setTimeout(() => {
                  // 打开文档查看
                  uni.openDocument({
                    filePath: res.savedFilePath,
                    success: function (res) {
                      // console.log('打开文档成功');
                    },
                  })
                }, 3000)
              },
            })
          }
        },
        fail: err => {
          console.log(err)
          uni.showToast({
            icon: 'none',
            mask: true,
            title: '失败请重新下载',
          })
        },
      })
      // #endif
    },
    uploadAPI(path) {
      uni.showLoading({
        title: '上传中',
      })
      const _this = this
      const fData = new FormData()
      fData.append('file', path)
      // fData.append("moduleName",'reports');
      const xhr = new XMLHttpRequest()
      const surl = this.api.host + '/api/shoppingcart/uploadExcel'
      xhr.open('POST', surl, true)
      // xhr.responseType = 'blob';
      xhr.onload = function (e) {
        // console.log("上传成功",e); //上传成功
      }
      xhr.onreadystatechange = () => {
        if (xhr.readyState == 4 && xhr.status == 200) {
          // 上传后台成功
          uni.hideLoading()
          const res = JSON.parse(xhr.responseText)
          this.fileUrl = res.data.file.key
          this.batchOrder(this.fileUrl)
          // _this.fileList.push(res.datas); // 上传成功后放进fileList数组用于展示
        } else {
          uni.hideLoading()
        }
      }

      // 不要使用协议头，不然传值有问题
      // xhr.setRequestHeader('Content-Type',"multipart/form-data");
      xhr.setRequestHeader('x-token', uni.getStorageSync('token'))
      xhr.send(fData)
    },
    batchOrder(fileUrl) {
      this.post('/api/shoppingcart/batchOrder', { link: fileUrl }, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.toast(res.msg)
            this.getBatchOrders()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    getBatchOrders() {
      this.get('/api/shoppingcart/getBatchOrders', {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.batchOrdersList = data.list
            this.failCount = data.fail_count
            this.successCount = data.success_count
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    bulkChange(type) {
      this.shipping_method_id = type
    },
    bulkClose(cancel) {
      // 获取组件的开启和关闭
      this.bulkShow = !cancel
    },
    closeDateLw(cancel) {
      // 关闭地址
      this.addressShow = !cancel
    },
    bulkDelCancel(cancel) {
      this.bulkDelShow = !cancel
    },
    addressShowOn(show) {
      this.addressShow = !show
      console.log(this.addressShow)
    },
    addressSetOn(address, addressName) {
      this.addressName = addressName
      this.addressShow = false
      ;({
        // 解构赋值
        city: this.bulkForm.city,
        city_id: this.bulkForm.city_id,
        county: this.bulkForm.county,
        county_id: this.bulkForm.county_id,
        province: this.bulkForm.province,
        province_id: this.bulkForm.province_id,
      } = address)
    },
    bulkDelConfirm(id) {
      let urlApi = ''
      if (id) {
        urlApi = '/api/shoppingcart/deleteBatchOrderById'
      } else {
        urlApi = '/api/shoppingcart/deleteBatchOrderByError'
      }
      this.delete(urlApi, { id }, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.bulkDelShow = false
            this.bulkDelId = 0 // 清空ID，不然删除订单错误
            this.getBatchOrders()
            setTimeout(() => {
              this.toast(res.msg)
            }, 500)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    bulkConfirm(form) {
      console.log(form)
      this.bulkForm = form
      this.bulkForm.qty = parseInt(form.qty)
      this.put('/api/shoppingcart/updateBatchOrder', this.bulkForm, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.bulkShow = false
            this.getBatchOrders()
            setTimeout(() => {
              this.toast(res.msg)
            }, 500)
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    edit(item) {
      ;({
        // 解构赋值
        city: this.bulkForm.city,
        city_id: this.bulkForm.city_id,
        county: this.bulkForm.county,
        county_id: this.bulkForm.county_id,
        detail: this.bulkForm.detail,
        id: this.bulkForm.id,
        mobile: this.bulkForm.mobile,
        province: this.bulkForm.province,
        province_id: this.bulkForm.province_id,
        product_name: this.bulkForm.product_name,
        purchase_sn: this.bulkForm.purchase_sn,
        qty: this.bulkForm.qty,
        realname: this.bulkForm.realname,
        sku2_name: this.bulkForm.sku2_name,
        sku_id: this.bulkForm.sku_id,
        sku_name: this.bulkForm.sku_name,
        user_id: this.bulkForm.user_id,
      } = item)
      console.log(this.bulkForm)
      console.log(item.product_name)
      this.addressName = item.province + item.city + item.county
      this.bulkShow = true
    },
    bulkDel(id) {
      this.bulkDelId = id
      this.bulkDelShow = true
      console.log(this.bulkDelShow)
    },
    bulkBatchDel() {
      if (this.failCount > 0) {
        this.bulkDelShow = true
      } else {
        this.toast('没有错误数据了')
      }
    },
    bulkBtn() {
      if (this.failCount === 0) {
        this.post(
          '/api/shoppingcart/createCartByBatchOrder',
          {
            shipping_method_id: this.shipping_method_id,
          },
          true,
        )
          .then(res => {
            if (res.code === 0) {
              const data = res.data
              this.toast(res.msg)
              const curRoute = this.routes()
              const redirectPageUrl = uni.setStorageSync(
                'redirect_page',
                curRoute,
              )
              setTimeout(() => {
                uni.switchTab({
                  url: '/pages/shoping_car/shoping_car',
                })
              }, 500)
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {
            // console.log(Error);
          })
      } else {
        this.toast('请编辑错误信息')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.download {
  margin: 22rpx 20rpx 20rpx 20rpx;
  width: 711rpx;
  height: 280rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  .upload-title {
    padding: 52rpx 64rpx 0 64rpx;
    .c-666 {
      line-height: 40rpx;
    }
  }
  .title {
    padding-top: 52rpx;
    .icon-fontclass-xiazai {
      color: #f14e4e;
      font-size: 40rpx;
      margin-right: 20rpx;
    }
    .icon-fontclass-shangchuan {
      color: #f14e4e;
      font-size: 40rpx;
      margin-right: 20rpx;
    }
  }
  .download-btn {
    width: 440rpx;
    height: 64rpx;
    border-radius: 4rpx;
    font-size: 28rpx;
    color: #ffffff;
  }
  .download-bg {
    background-color: #1890ff;
  }
  .upload-bg {
    // margin: 80rpx auto 0 auto;
    background-color: #f14e4e;
  }
}
.down-hint {
  margin: 0 20rpx;
  .text {
    line-height: 50rpx;
    margin: 16rpx 30rpx 0 55rpx;
  }
}
.uploadtitle {
  position: absolute;
  bottom: 12rpx;
  left: 0;
  right: 0;
}

.filesBox {
  margin-top: 28rpx;
}

.fileslist {
  margin-top: 8px;
}

.upfileico {
  width: 52rpx;
  height: 52rpx;
  display: inline-block;
  vertical-align: top;
}

.upname {
  font-size: 26rpx;
  color: #333333;
  width: 80%;
  display: inline-block;
}

.upclose {
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  float: right;
  position: relative;
  top: 3px;
}

.batch-orders {
  .batch-head {
    background-color: #fff;
    .succeed {
      margin: 48rpx 0 40rpx 0;
      .icon-all_select_active {
        color: #29b11b;
        font-size: 48rpx;
      }
    }
    .batch-hint {
      padding: 0 88rpx;
      color: #666666;
      line-height: 40rpx;
      font-size: 24rpx;
      text-align: center;
    }
    .remark {
      margin: 58rpx 0 20rpx 0;
      .icon-fontclass-gantanhao {
        width: 25rpx;
        height: 25rpx;
        color: #f14e4e;
        margin: 0 14rpx 0 30rpx;
      }
      view {
        color: #f14e4e;
        font-size: 22rpx;
        line-height: 36rpx;
      }
    }
  }
  .orders-list {
    margin: 20rpx;
    .orders-item {
      border-radius: 10rpx;
      background-color: #fff;
      padding: 0 30rpx 28rpx 30rpx;
      margin-bottom: 20rpx;
      .title {
        height: 78rpx;
        line-height: 78rpx;
        color: #828282;
        font-size: 22rpx;
        .orders-title-left {
          .serial {
            margin-right: 35rpx;
          }
        }
        .orders-title-right {
          width: 108rpx;
          .icon-fontclass-qianshu {
            // margin-right: 44rpx;
          }
        }
      }
      .orders-main {
        margin-top: 30rpx;
        color: #2f2f2f;
        font-size: 26rpx;
        .order-attribute {
          margin-bottom: 30rpx;
        }
      }
    }
  }
}
.bulk-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 26rpx 30rpx 0 30rpx;
  background-color: #fff;
  .distribution {
    .manner {
      text {
        color: #2f2f2f;
        font-size: 24rpx;
        margin-right: 14rpx;
      }
    }
  }
  .total-list {
    color: #818181;
    line-height: 36rpx;
    margin: 10rpx 0;
    font-size: 22rpx;
  }
  .bulk-group {
    .bulk-btn {
      border-radius: 4rpx;
      border: solid 1rpx #a1a1a1;
      padding: 14rpx 28rpx;
      margin-right: 48rpx;
      font-size: 22rpx;
      position: relative;
      // &::before {
      // 	content: "";
      // 	position: absolute;
      // 	left:0;
      // 	top: 0;
      // 	width: 200%;
      // 	height: 200%;
      // 	border: 1px solid #a1a1a1;
      // 	/* 以（0,0）为放缩中心点 */
      // 	transform-origin: 0  0;
      // 	transform: scale(0.5);
      // }
    }
    .red-btn {
      background-color: #f14e4e;
      border: 0rpx;
      color: #fff;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 200%;
        height: 200%;
        border: none;
        /* 以（0,0）为放缩中心点 */
        transform-origin: 0 0;
        transform: scale(0.5);
      }
    }
  }
  .bulk-order-msg {
    color: #f14e4e;
    margin: 25rpx 0 20rpx 0;
    text-align: center;
    font-size: 20rpx;
  }
}
</style>

