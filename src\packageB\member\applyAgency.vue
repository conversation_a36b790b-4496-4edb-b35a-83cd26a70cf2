﻿<!-- 申请代理 -->
<template>
	<view>
		<apply-head :applyStatus="status" :isApply="isApply" type="agency"></apply-head>
		<view class="audit d-c" v-if="status === 0 && isApply">
			<image src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/audit.png">
			</image>
		</view>
		<view class="audit d-c" v-if="status === 1 && isApply">
			<image src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/pass.png">
			</image>
		</view>
		<view class="audit d-c" v-if="status === -1 && isApply">
			<image src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/auditFailure.png">
			</image>
		</view>
		<view class="steps_form" v-show="status === 0 && isApply === 0">
			<u--form :model="agencyForm" ref="agencyForm" :rules="ruleValidate">
				<u-form-item borderBottom label="真实姓名" labelWidth="180rpx" prop="agencyNmae" :required="true"
					style="margin:0 26rpx 30rpx 26rpx;" labelStyle="{'fontSize':'28rpx'}">
					<u--input v-model="agencyForm.agencyNmae" border="none"
						placeholderStyle="text-align:right;font-size:28rpx" placeholder="请输入您的姓名"
						style="text-align: right;"></u--input>
				</u-form-item>
				<u-form-item label="联系方式" labelWidth="180rpx" prop="mobile" borderBottom :required="true"
					style="margin:0 26rpx 30rpx 26rpx;">
					<u--input v-model="agencyForm.mobile" placeholderStyle="text-align:right" placeholder="请输入您的联系方式"
						style="text-align: right;" border="none"></u--input>
				</u-form-item>
				<u-form-item label="代理等级" labelWidth="180rpx" ref="level" prop="level" borderBottom :required="true"
					style="margin:0 26rpx 30rpx 26rpx;">
					<view class="d-cf">
						<text class="c-71 font_size14" @click="agencyOpen" >{{agencyName || '请选择代理等级'}}</text>
						<text class="iconfont icon-member_right"></text>
					</view>
				</u-form-item>
				<u-form-item v-if="agencyForm.level !== null"  label="省级区域" labelWidth="180rpx" prop="province_id"
					borderBottom :required="true" style="margin:0 26rpx 30rpx 26rpx;">
					<view class="d-cf" @click="address('province')">
						<view class="agencyArea">{{provinceName}}</view>
						<text class="iconfont icon-member_right"></text>
					</view>
				</u-form-item>
				<u-form-item v-if="agencyForm.level === 2 || agencyForm.level === 3 || agencyForm.level === 4"
					label="市级区域" labelWidth="180rpx" prop="city_id" borderBottom :required="true"
					style="margin:0 26rpx 30rpx 26rpx;">
					<view class="d-cf" @click="address('city')">
						<view class="agencyArea">{{cityName}}</view>
						<text class="iconfont icon-member_right"></text>
					</view>
				</u-form-item>
				<u-form-item v-if=" agencyForm.level === 3 || agencyForm.level === 4" label="区级区域" labelWidth="180rpx"
					prop="county_id" borderBottom :required="true" style="margin:0 26rpx 30rpx 26rpx;">
					<view class="d-cf" @click="address('county')">
						<view class="agencyArea">{{countyName}}</view>
						<text class="iconfont icon-member_right"></text>
					</view>
				</u-form-item>
				<u-form-item v-if="agencyForm.level === 4" label="街级区域" labelWidth="180rpx" prop="town_id" borderBottom
					:required="true" style="margin:0 26rpx 30rpx 26rpx;">
					<view class="d-cf" @click="address('town')">
						<view class="agencyArea">{{townName }}</view>
						<text class="iconfont icon-member_right"></text>
					</view>
				</u-form-item>
			</u--form>
		</view>

		<view class="supplierBtn d-cc" v-if="status === 0 && isApply === 0" @click="agencyFormBtn">提交</view>
		<view class="mb30"></view>

		<u-popup 
				:show="agreementShow"
				v-if="agreementSwitch"
				:round="20"
				@close="close('agreement')" 
				@open="open" 
				mode="center">
			<view class="main">
				<view class="header d-c">
					<view class="title">
						申请协议
					</view>
				</view>
				<view class="content" v-html="agreement"></view>
				<view class="agreement-btn" @click="close('agreement')">确定</view>
			</view>
		</u-popup>

		<u-popup :show="agencyShow" :round="10" mode="bottom" @close="close('agency')" @open="agencyOpen">
			<view class="classify">
				<view class="classify_title d-bf">
					<view class="c-b5 font_size13" @click="close('agency')">取消</view>
					<view class="c-20 font_size17">选择代理等级</view>
					<view class="c-orange font_size13" @click="confirm('agency')">确认</view>
				</view>
				<view class="classify_content">
					<u-radio-group placement="column" iconPlacement="right">
						<u-radio size="30" activeColor="#f15353" labelSize="28rpx" labelColor="#202020"
							v-for="(item, index) in agencyList" :key="index" :label="item.name" :name="item.value"
							@change="checkboxChange(item.value,item.name)">
						</u-radio>
					</u-radio-group>
				</view>
			</view>
		</u-popup>

		<u-popup :show="addressShow" @close="close('address')" @open="open">
			<view class="popup-return-type">
				<u-navbar :title="siteTitle[index]">
					<view class="u-nav-slot" slot="left" @click="close('address')">
						<u-icon name="arrow-left" size="40"></u-icon>
					</view>

				</u-navbar>
				<view class="u-page">
					<u-list>
						<u-list-item v-for="(item, index) in provinceData" :key="index">
							<u-cell :title="item.name" :isLink="true"
								@click="provinceBtn(item.name,item.id,item.level)">
							</u-cell>
						</u-list-item>
					</u-list>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
		export default {
				data() {
			return {
				status: 0, //设置不同状态
				isApply: 0,
				index: 0,
				agreementSwitch:0,
				addressId: 0,
				provinceData: [],
				provinceName: '',
				cityName: '',
				countyName: '',
				townName: '',
				agreement: '',
				agencyName: '',
				levelName: '',
				agreementShow: false,
				agencyShow: false,
				addressShow: false,
				siteTitle: ['省级区域', '市级区域', '区/县级区域', '街道/乡镇区域'],
				agencyList: [{
						name: '省级代理',
						value: 1
					},
					{
						name: '市级代理',
						value: 2
					},
					{
						name: '区级代理',
						value: 3
					},
					{
						name: '街级代理',
						value: 4
					}
				],
				levelPopup: 0,
				agencyForm: {
					city_id: null,
					county_id: null,
					level: null,
					province_id: null,
					real_name: '',
					mobile: '',
					town_id: null,
					uid: 0,
					agencyNmae: ''
				},
				ruleValidate: {
					agencyNmae: [
						{
							required: true,
							message: '请输入您的姓名',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 15,
							message: '长度在2-15个字符之间',
							trigger: ['blur']
						},
					],
					mobile: [{
							required: true,
							message: '请输入您的联系方式',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					level: [{
						type: 'number',
						required: true,
						message: '请选择代理等级',
						trigger: ['blur']
					}],
					province_id: [{
						type: 'number',
						required: true,
						message: '请选择省',
						trigger: ['blur']
					}],
					city_id: [{
						type: 'number',
						required: true,
						message: '请选择市',
						trigger: ['blur']
					}],
					county_id: [{
						type: 'number',
						required: true,
						message: '请选择区',
						trigger: ['blur']
					}],
					town_id: [{
						type: 'number',
						required: true,
						message: '请选择街道',
						trigger: ['blur']
					}],
				}
			}
		},
		onReady() {
			//处于表单审核中会报错
			this.$refs.agencyForm.setRules(this.ruleValidate); //兼容小程序
		},
		onLoad() {
			this.agencyApplyStatus(); //申请代理状态
			this.findSetting(); //代理协议
		},
		onShow() {
			
			
		},
		methods: {
			close(type) {
				console.log(type);
				if (type === 'agreement') {
					this.agreementShow = false;
				}
				if (type === 'agency') {
					this.agencyShow = false;
				}
				if (type === 'address') {
					this.addressShow = false;
				}
			},
			confirm(type) {
				if (type === 'agency') {
					this.agencyShow = false;
					this.agencyForm.level = this.levelPopup;
					this.agencyName = this.levelName;
					this.$refs.agencyForm.validateField('level') //重新校验一次
				}
			},
			agencyOpen() {
				this.agencyShow = true;
			},
			checkboxChange(value, name) {
				console.log(name)
				// this.agencyForm.level = value;
				this.levelPopup = value;
				this.levelName = name;
				console.log(value);
			},
			address(site) { //选择代理等级
				if (site === 'province') {
					console.log('province');
					this.addressShow = true;
					this.index = 0;
					this.addressList(this.addressId);
				} else if (site === 'city') {
					if (this.agencyForm.province_id) {
						this.addressShow = true;
						this.index = 1;
						this.addressList(this.agencyForm.province_id);
					} else {
						this.toast('请先选择省级');
					}
				} else if (site === 'county') {
					if (this.agencyForm.city_id) {
						this.addressShow = true;
						this.index = 2;
						this.addressList(this.agencyForm.city_id);
					} else {
						this.toast('请先选择市级');
					}
				} else if (site === 'town') {
					if (this.agencyForm.county_id) {
						this.addressShow = true;
						this.index = 3;
						console.log(this.agencyForm.county_id);
						this.addressList(this.agencyForm.county_id);
					} else {
						this.toast('请先选择区级');
					}
				} else {
					console.log(this.agencyForm.county_id);
					if (this.agencyForm.town_id) {
						this.addressShow = true;
						this.index = 3;
					} else {
						this.toast('请先选择区级');
					}
				}
			},
			addressList(id) {
				this.get('/api/region/list?parent_id=' + id, {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						console.log(this.id);
						this.provinceData = data.list;
						console.log(this.provinceData);
						// let provinceData = data.list;


					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			provinceBtn(name, id, level) { //点击省级的时候，赋值给市级的接口渲染地址数据，后面都一样
				if (level === 1) {
					this.provinceName = name;
					this.agencyForm.province_id = id;
					this.$refs.agencyForm.validateField('province_id') //重新校验一次
				} else if (level === 2) {
					this.cityName = name;
					this.agencyForm.city_id = id;
					this.$refs.agencyForm.validateField('city_id') //重新校验一次
				} else if (level === 3) {
					this.countyName = name;
					this.agencyForm.county_id = id;
					console.log(this.agencyForm.county_id);
					this.$refs.agencyForm.validateField('county_id') //重新校验一次
					console.log(id);

				} else {
					this.townName = name;
					console.log(id);
					this.agencyForm.town_id = id;
					this.$refs.agencyForm.validateField('town_id') //重新校验一次
				}
				this.addressShow = false;
			},
			agencyApplyStatus() {
				this.get('/api/areaAgency/getApplyStatus', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.isApply = data.isApply;
						this.status = data.status;
						if(this.isApply == 0&&this.status==0){
							this.agreementShow = true
						}
						if (data.status === -1) {
							this.toast('您申请的代理被驳回，请重新申请');
						}
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			findSetting() {
				this.get('/api/areaAgency/findSetting', {}, true).then((res) => {
					if (res.code === 0) {
						let data = res.data;
						this.agreement = data.setting?.value?.agreement;
						this.agreementSwitch =  data.setting?.value?.agreement_switch;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			agencyFormBtn() { //申请代理
				console.log(this.agencyForm);
				this.$refs.agencyForm.validate().then(res => {
					this.post('/api/areaAgency/createAgencyApply', this.agencyForm, true).then((res) => {
						if (res.code === 0) {
							let data = res.data;
							this.toast('申请成功');
							
							setTimeout(() => {
								this.agencyApplyStatus();
								this.$refs.agencyForm.resetFields();
							}, 1000)

						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				}).catch(errors => {
					uni.$u.toast('填写错误')
				})

			}
		}
	}
</script>
<style scoped>
	.popup-return-type ::v-deep .u-navbar--fixed {
		position: relative;
	}

	/* 	/deep/ .popup-return-type  .u-line {
		border-bottom: none!important;
	} */
	.steps_form ::v-deep .u-form-item__body__right__content__slot {
		justify-content: flex-end;
		flex-direction: row; /*兼容小程序*/
	}

	.steps_form ::v-deep .u-form-item__body__right__message {
		text-align: right;
	}

	.steps_form  ::v-deep .u-input__content__field-wrapper .u-input__content__field-wrapper__field {
		text-align: right !important;
	}

	.classify_content ::v-deep .u-radio {
		margin-bottom: 53rpx;
	}
</style>
<style lang="scss" scoped>
	/*#ifdef MP-WEIXIN*/
	.steps_form::v-deep .u-form-item {
		padding:0rpx 30rpx 26rpx 30rpx;
	}
	/*#endif*/
	.audit {
		box-sizing: border-box;
		margin: 20rpx 20rpx 0 20rpx;
		background-color: #fff;
	}

	.steps_form {
		margin: 20rpx;
		padding-bottom: 1rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.agencyArea {
			text-align: right;
			width: 200rpx;
			height: 100%;
		}

		.supplier {
			width: 140rpx;
			height: 140rpx;
			border-radius: 10rpx;
			border: solid 2rpx #a5a5a5;
		}
	}

	.supplierBtn {
		width: 650rpx;
		height: 70rpx;
		position: relative;
		background-color: #f14e4e;
		border-radius: 6rpx;
		margin: 70rpx auto 0rpx auto;
		color: #fff;
	}

	.main {
		width: 640rpx;
		// height: 1024rpx;
		border-radius: 20rpx;
		margin: 0 auto;
		background: #fff;

		.header {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			position: relative;

			.title_close {
				position: absolute;
				right: 20rpx;
			}
		}

		.content {
			margin: 0 34rpx 0 24rpx;
			height: 768rpx;
			overflow-y: scroll;

		}

		.agreement-btn {
			width: 408rpx;
			height: 74rpx;
			background-color: rgb(240, 77, 77);
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: rgb(255, 255, 255);
			font-size: 40rpx;
			margin: 48rpx auto 34rpx;
		}
	}

	/*弹出框样式*/
	.classify {
		padding: 40rpx 30rpx 30rpx 30rpx;

		.classify_content {
			margin-top: 50rpx;
		}
	}
</style>

