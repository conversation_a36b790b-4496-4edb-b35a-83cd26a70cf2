﻿<template>
  <view>
    <view class="my_order mb-20 b-r-10">
      <view class="order_title font_size14 f-bold mb-20">
        {{ mYtitle }}
      </view>
      <u-line></u-line>
      <view class="order_content">
        <u-grid :border="false" col="5">
          <u-grid-item v-for="(listItem, listIndex) in mylist" :key="listIndex"
            @click="onClickNav(listItem.title, listItem.url)">
            <view class="iconfont" :class="listItem.nameClass" v-if="listItem.nameClass"></view>
            <image :src="listItem.nameImg" v-if="listItem.nameImg" mode="widthFix" class="iconf-img" />
            <text class="grid-text font_size12">
              {{ listItem.title }}
            </text>
          </u-grid-item>
        </u-grid>
      </view>
    </view>
    <view>
      <u-popup :show="popShow" @close="close">
        <view class="d-c mt_40 fs-3">小商店申请</view>
        <view class="con-pop">
          <view class="mb-40">
            <u-input placeholder="请输入小商店名称" border="surround" fontSize="24rpx" v-model="value"></u-input>
          </view>
          <view>
            <u-button type="error" size="small" shape="circle" text="提交申请" @click="onSubmit"></u-button>
          </view>
        </view>
      </u-popup>
    </view>
    <u-modal :show="modalShow" :title="title" :content="content" :showCancelButton="true" @confirm="
          modalShow = false
        popShow = true
          " @cancel="modalShow = false"></u-modal>
    <u-popup bgColor="transparent" :show="isShow" mode="center" @close="closeShow">

      <!-- @click="sm" show-menu-by-longpress class="ewmImg" -->
      <view class="bg">
        <u--image  width="600" height="900" :src="qr_code_url"></u--image>
      </view>
      
      <view class="h30"></view>
      <u-button class="uButton" type="error" size="small" shape="circle" text="提交申请" @click="fksj">扫码识别</u-button>
    </u-popup>

  </view>
</template>
<script>
import { applyStatus } from '@/mixin/applyStatus'

export default {
  name: 'myBeauty',
  mixins: [applyStatus],
  props: {
    mYtitle: String,
    mylist: Array,
    // apply_status: Number, // 0-未申请；1-驳回；-1-通过
    // tip: String,
    isShowShop: Number,
  },
  data() {
    return {
      // apply_status: 0,
      popShow: false,
      value: '',
      modalShow: false,
      title: '提示',
      content: '是否重新提交小程序申请？',
      isShow: false,
      qr_code_url: '',
      fmPosterShow: false
    }
  },
  methods: {
    deletePic() {
      console.log(111);
    },
    afterRead() {
      console.log(222);
    },
    closeFm() {
      this.defaultImg = "";
      this.fmPosterShow = false;
    },
    async fksj() {
      //  获取订单id
      let res = await this.post('/api/institution/purchase')
      if (res.code === 0) {
        uni.setStorageSync('orderIds', res.data.order_ids);
        uni.setStorageSync('pageSourse', '');
        this.navTo('/packageD/agencies/distributorPayment');
      }
    },
    // 长按识别二维码
    
    async onClickNav(title, url) {
      if (title === '申请开店') {
        let data = await this.getApplyStatus()
        switch (
        data.apply_status // 0-未申请；1-驳回；-1-通过；-2-需要升级付费会员
        ) {
          case 0:
            this.popShow = true
            break
          case 1:
            this.popShow = true
            break
          case -1:
            this.navTo(url)
            break
          case -2:
            this.memberPay()
            break
          default:
            break
        }
      } else if (title === '代理升级') {
        // 获取海报
        this.get('/api/institution/getAgentPoster').then(res => {
          if (res.code === 0) {
            this.qr_code_url = res.data.link
            this.isShow = true
          }
        })

        // 获取订单id
        // let res = await this.post('/api/institution/purchase')
        // if (res.code === 0) {
        //   uni.setStorageSync('orderIds', res.data.order_ids);
        //   this.navTo(url)
        // }
      } else {
        this.navTo(url)
        // this.qr_code_url = '../../static/image/sm.png'
        // this.isShow = true
      }
    },
    onSubmit() {
      // 判断是否为扫描打开，如果是，则在点击申请的时候判断是否可以申请
      if(uni.getStorageSync("smShop") === 'smShop'){
        uni.removeStorageSync('smShop')
        this.getApplyStatus().then(res => {
          if (res.apply_status === -2) {
            this.memberPay()
          }
        })
      }

      const params = {
        title: this.value,
      }
      this.post('/api/smallShop/apply/subApply', params, true)
        .then(res => {
          if (res.code === 0) {
            // this.close()
            this.goAudit()
          }
        })
        .catch(Error => {
          console.log(Error)
        })
      this.close()
    },
    close() {
      this.popShow = false
    },
    goAudit() {
      this.navTo('/packageD/smallShop/smallShopAudit')
    },
    // 会员付费
    async memberPay() {
      this.toast(this.tip)
      const params = {
        page: 1,
        pageSize: 12,
        level_ids: this.level_ids
      }
      this.post('/api/user/getRenewLevels', params).then(res => {
        if (res.code === 0) {
          this.navTo('/packageD/memberLevel/memberRight')
        }
      })
    },
    closeShow() {
      this.isShow = false;
      this.qr_code_url = ''
    }
  },
}
</script>
<style scoped></style>
<style lang="scss" scoped>
.con-pop {
  padding: 30rpx 60rpx 60rpx 60rpx;
  height: 200rpx;
}

.my_order {
  background-color: #fff;
  padding: 25rpx 30rpx 30rpx 30rpx;
}

.order_content {
  .iconfont {
    font-size: 48rpx;
    margin: 42rpx 0 14rpx 0;
  }

  .icon_hbcolor{
    color: #38BC4E;
    font-size: 40rpx;
  }
  .icon_tuiguangCol{
    color: #3272FD;
    font-size: 40rpx;
  } 

  //设置图标的样式
  .icon_blue {
    font-size: 40rpx;
    color: #21a9f5;
  }

  .icon_orange {
    font-size: 40rpx;
    color: #f3702d;
  }
  .icon_green {
    font-size: 40rpx;
    color: #38BC4E;
  }

  .icon_violet {
    font-size: 40rpx;
    color: #a558ed;
  }

  .iconf-img {
    margin: 34rpx 0 10rpx 0;
    width: 56rpx;
    height: 56rpx;
    display: block;
  }

  .icon_small_shop {
    margin-bottom: 3rpx;
  }

  .icon_yellow {
    font-size: 40rpx;
    color: #f3a51d;
  }

  .icon_myVideo {
    font-size: 40rpx;
    color: #A258EF;
  }
  
  .icon_myMaterial {
    font-size: 40rpx;
    color: #3272FD;
  }
}


.ewmImg {
  width: 600rpx;
  height: 700rpx;
}

.uButton{
  width: 600rpx;
  margin-top: 20rpx;
}

.h30{
  width: 30rpx;
}

.bg{
  background-color: #fff;
}


</style>

