﻿<template>
  <view>
    <u-popup :show="isShow">
      <view>
        <view class="title mt_10 mb_10">用户隐私保护提示</view>
        <view class="pl_10 pr_10 content-view">
          <view>欢迎来到[{{ tabTitle }}}]！</view>
          <view class="mt_10">
            为了更好地提供浏览推荐、发布信息购买商品、交流沟通、注册认证等相关服务，我们会根据您使用的服务的需要向您收集必要的用户信息（包括但不限于账户信息、交易信息等）
            <text class="agreement-text" @click="goToPrivacy">
              《用户隐私保护指引》
            </text>
            。末经您的同意，我们不会对外提供您的相关信息。您可以浏览修改或删除您的个人信息，我们届时会为您提供注销、投诉渠道。
            向您提供服务以保障您的隐私信息安全可控。
          </view>
          <view class="pri_btn mt_20">
            <button class="confuse_btn" @click="confusePrivacy">拒绝</button>
            <button
              class="confirm_btn"
              id="agree-btn"
              open-type="agreePrivacyAuthorization"
              @agreeprivacyauthorization="handleAgreePrivacyAuthorization"
            >
              同意
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
export default {
    name: 'myPrivacyAuthPopup',
  data() {
    return {
      isShow: false,
      resolvePrivacyAuthorization: {},
    }
  },
  computed: {
    tabTitle() {
      return uni.getStorageSync('tabTitle') || ''
    },
  },
  created() {
    this.onNeedPrivacyAuthorization()
  },
  methods: {
    // 同意
    handleAgreePrivacyAuthorization() {
      // 告知平台用户已经同意，参数传同意按钮的id
      this.resolvePrivacyAuthorization({
        buttonId: 'agree-btn',
        event: 'agree',
      })
      this.isShow = false
    },
    // 拒绝
    confusePrivacy() {
      this.isShow = false
      this.resolvePrivacyAuthorization({ event: 'disagree' })
    },
    // 打开隐私协议
    goToPrivacy() {
      wx.openPrivacyContract({
        success: () => {
          console.log('打开成功')
        }, // 打开成功
        fail: () => {
          uni.showToast({
            title: '打开失败，稍后重试',
            icon: 'none',
          })
        }, // 打开失败
      })
    },
    onNeedPrivacyAuthorization() {
      if (!wx.onNeedPrivacyAuthorization) {
        return
      }
      let _this = this
      wx.onNeedPrivacyAuthorization(resolve => {
        _this.isShow = true
        _this.resolvePrivacyAuthorization = resolve
      })
    },
  },
}
</script>
<style scoped>
.title {
  text-align: center;
  font-size: 40rpx;
}
.content-view{
    font-size: 32rpx;
}
.agreement-text {
  color: rgb(41, 121, 255);
}
.pri_btn{
    display: flex;
}
.pri_btn button{
    flex: 1;
    border: 0;
}
.pri_btn button:last-child{
    background-color: #e2777a;
    color: #ffffff;
    margin-left: 5px;
}
.pri_btn button:first-child{
    background-color: #d0d0d0;
    margin-right: 5px;
}
</style>

