﻿<!-- 协议的弹框组件 -->
<template>
	<view>
		<view class="agreement font_size11 f fac">
			<u-checkbox-group
            placement="column"
            @change="checkboxChange"
			:size="30"
        >
            <u-checkbox
				activeColor="#F15353"
                :customStyle="{marginBottom: '1px'}"
				name="true"
            >
            </u-checkbox>
        </u-checkbox-group>
			我已阅读并同意<text class="c-orange" @click="agreement">《用户协议》</text>
		</view>
		<u-popup :show="protocolShow" @close="close" mode="center" :round="20">
						<view class="main">
								<view class="header d-c">
									<view class="title">
									</view>
									<view class="iconfont icon-close11 title_close" @click="close"></view>
								</view>
								<view class="content" v-html="agreements"></view>
								<view class="agreement-btn" @click="close">确定</view>
						</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
    name: 'myAgreement',
		props:{
			agreemenTtype : {
				type:String,
				default:''
			}
		},
		data() {
			return {
				protocolShow:false,
				content:'',
				agreements:''
			}
		},
		mounted() {
			
		},
		methods: {
			checkboxChange(v){
				let pass = v && v.length ? true : false
				this.$emit('confirmed',pass)
			},
			close() {
				this.protocolShow = false;
			},
			agreement() {
				this.protocolShow = true;
				this.findSetting();
			},
			findSetting() {
				this.post('/api/user/findSetting', ).then(res => {
					let code = res.data.code;
					if(res.code === 0) {
						let data = res.data;
						this.agreements = data.setting.value.agreement;
					} else {
						this.toast(res.data.msg);
					}
				}).catch(Error => {
					console.log('网络错误' + Error);
				})
			}
		}
	}
</script>
<style scoped>
	.content ::v-deep p img{
		width: 100%;
		height: 100%;
		display: block;
	}
</style>
<style lang="scss" scoped>
	.main {
		width: 640rpx;
		height: 1024rpx;
		border-radius: 20rpx;
		margin: 0 auto;
		background: #fff;
		.header {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			position: relative;
			.title_close {
				position: absolute;
				right:20rpx;
			}
		}
		.content {
			margin: 0 34rpx 0 24rpx;
			height: 768rpx;
			overflow-y: scroll;
			
		}
		.agreement-btn {
			width: 408rpx;
			height: 74rpx;
			background-color: rgb(240, 77, 77);
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: rgb(255, 255, 255);
			font-size: 40rpx;
			margin: 48rpx auto 34rpx;
		}
	}
</style>

