﻿<template>
  <u-tabbar
    :value="tabBarActive"
    activeColor="#f15353"
    inactiveColor="#161616"
    @change="handleChange"
  >
    <u-tabbar-item v-for="(item, index) in list" :key="index" :text="item.text">
      <image
        class="u-page__item__slot-icon"
        slot="active-icon"
        :class="item.selectedIconPath"
        style="color: #F15353"
      ></image>
      <image
        class="u-page__item__slot-icon"
        slot="inactive-icon"
        :class="item.iconPath"
      ></image>
    </u-tabbar-item>
  </u-tabbar>
</template>
<script>
export default {
    name: 'myTabbar',
  created() {
    this.getBottomNavigation()
  },
  data() {
    return {
      tabBarActive: 0,
      list: [
        {
          pagePath: '/pages/index/index',
          iconPath: 'iconfont icon-shouye',
          selectedIconPath: 'iconfont icon-shouye',
          text: '首页',
        },
        {
          pagePath: '/pages/classify/classify',
          iconPath: 'iconfont icon-fontclass-fenlei',
          selectedIconPath: 'iconfont icon-fontclass-fenlei',
          text: '分类',
          // visible: false,
        },
        {
          pagePath: '/pages/promotion/promotion',
          iconPath: 'iconfont icon-tuiguang',
          selectedIconPath: 'iconfont icon-tuiguang',
          text: '推广',
          // visible: false,
        },
        {
          pagePath: '/pages/shoping_car/shoping_car',
          iconPath: 'iconfont icon-gouwuche3',
          selectedIconPath: 'iconfont icon-gouwuche3',
          text: '购物车',
          // visible: false,
        },
        {
          pagePath: '/pages/membercenter/membercenter',
          iconPath: 'iconfont icon-geren',
          selectedIconPath: 'iconfont icon-geren',
          text: '会员中心',
          // visible: false,
        },
      ],
    }
  },
  methods: {
    // 之前隐藏的tabbar
    /* async getAgentSetting() {
      const { code, data } = await this.post('/api/user/findAgentSetting', {})
      if (code === 0) {
        // 0=隐藏 1=显示
        const show = data.setting.show_agent_button
        this.$store.commit('updateAgentShow', show === 0 ? false : true)
        if (show === 0) {
            this.list.splice(2,1)
        }
      }
    }, */
    async getBottomNavigation() {
      const { code, data } = await this.post(
        '/api/article/getWebBottomNavigationSetting',
        {},
      )
      if (code === 0) {
        uni.setStorageSync('showPromotion', true)
        // 0=显示 1=隐藏
        if (data.webBottomNavigationSetting.is_classify_navigation === 1) {
          this.list = this.list.filter(item => item.text !== '分类')
          uni.setStorageSync('tabPromotion', 1)
        } else {
          uni.setStorageSync('tabPromotion', 2)
        }
        if (data.webBottomNavigationSetting.is_promotion_navigation === 1) {
          this.list = this.list.filter(item => item.text !== '推广')
          uni.setStorageSync('showPromotion', false)
        }
        if (data.webBottomNavigationSetting.is_shopping_cart_navigation === 1) {
          this.list = this.list.filter(item => item.text !== '购物车')
        }
        if (data.webBottomNavigationSetting.is_member_center_navigation === 1) {
          this.list = this.list.filter(item => item.text !== '会员中心')
        }
      }
    },
    // tabbar切换 跳转路由
    handleChange(index) {
      this.tabBarActive = index
      uni.setStorageSync('tabBarActive', index)
      this.$emit('getListLength',this.list.length)
      this.navsTo(this.list[index].pagePath)
    },
  },
}
</script>
<style lang="scss" scoped>
.u-page__item__slot-icon {
  width: 60rpx;
  height: 60rpx;
  font-size: 40rpx;
  line-height: 60rpx;
  text-align: center;
}
</style>

