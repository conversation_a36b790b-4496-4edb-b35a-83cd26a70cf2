﻿<!-- 搜索结果 -->
<template>
  <view>
    <!-- 搜索框 -->
    <view class="d-bf pl-25 pr_20 pt_14 pb_14 bg-white" v-if="!enterAlbum">
      <view class="d-cc">
        <view
          class="iconfont icon-member-left fs-0"
          style="color: #747474"
          @tap="navigateBack"
        ></view>
        <view class="ml-15">
          <view>
            <view style="width: 680rpx">
              <u-search
                height="60"
                bgColor="#eff0f1"
                color="#666666"
                v-model="searchValue"
                searchIconSize="40"
                @search="merchandiseNews"
                @custom="merchandiseNews"
              ></u-search>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--  -->
    <my-screening-of-goods
      type="screen"
      :list="list"
      :user="user"
      @pMerchandiseNews="merchandiseNews"
      :enterAlbum="enterAlbum"
    ></my-screening-of-goods>
  </view>
</template>

<script>
export default {
    data() {
    return {
      searchValue: '',
      classifyValue: '',
      user: null, // 用户信息
      classifIedinto: false, // 是否是分类进入
      enterAlbum: false, // 是否专辑进入
      albumParameters: {
        // 专辑参数
        page: 1,
        pageSize: 5,
        albumId: '', // 专辑id
      },
      list: [], // 商品列表数据
      list2: '', // 普通商品总共多少页当前多少页
      list3: '', // 专辑商品总共多少页当前多少页
    }
  },
  onLoad(opation) {
    if (this.checkNull(opation.value)) {
      // 搜索页面跳转
      this.searchValue = decodeURI(opation.value)
      this.$store.commit('upSearchGoods', {
        // 搜索店铺
        supplier_id: opation.supplierId,
      })
      this.merchandiseNews()
    } else if (this.checkNull(opation.collection_id)) {
      // 首页专辑跳转
      this.enterAlbum = true
      this.albumParameters.albumId = opation.collection_id
      this.listByCollectionIdLogin()
    } else if (
      this.checkNull(opation.category1_id) ||
      this.checkNull(opation.category2_id) ||
      this.checkNull(opation.category3_id) ||
      this.checkNull(opation.supplierId)
    ) {
      // 首页分类跳转
      this.classifIedinto = true
      if (!opation.category1_id) {
        opation.category1_id = ''
      }
      if (!opation.category2_id) {
        opation.category2_id = ''
      }
      if (!opation.category3_id) {
        opation.category3_id = ''
      }
      if (!opation.supplierId) {
        opation.supplierId = ''
      }
      this.$store.commit('upSearchGoods', {
        category1_id: opation.category1_id,
        category2_id: opation.category2_id,
        category3_id: opation.category3_id,
        supplier_id: opation.supplierId,
      })
      this.merchandiseNews('classify')
    }
    this.user = uni.getStorageSync('user')
  },
  methods: {
    onHide() {
      this.replacementPriceFilter()
    },
    onUnload() {
      this.replacementPriceFilter()
    },
    merchandiseNews(stair) {
      // 商品信息展示
      let site = '' // 首页分类类型进入要加的参数
      const json = this.$store.state.searchGoods // 要传的参数
      if (stair != 'refresh') {
        // 除了下拉刷新以外所有的筛选都是从第一页开始
        this.$store.commit('upSearchGoods', {
          page: 1,
        })
      }
      if (this.classifIedinto) {
        site =
          '&category1_id=' +
          json.category1_id +
          '&category2_id=' +
          json.category2_id +
          '&category3_id=' +
          json.category3_id
      }
      const apiUrl = uni.getStorageSync('user') ? '/api/product/listLogin' : '/api/product/list'
      // if(this.checkNull(this.user)) apiUrl = "/api/product/listLogin"
      /* let url = apiUrl + `?page=${json.page}&pageSize=${json.pageSize}
				&title=${this.searchValue}&supplier_id=${json.supplier_id}&sort_by=${json.sort_by}&price_form=${this.toFen(json.price_form)}
				&price_to=${this.toFen(json.price_to)}&profit_form=${this.toFen(json.profit_form)}&profit_to=${this.toFen(json.profit_to)}${site}` */
      /**
				 * {
						"category1_id": 1080,
						"category2_id": 1081,
						"category3_id": 1082,
						"origin_rate": {
							"from": null,
							"to": null
						},
						"min_price": null,
						"max_price": null,
						"is_new": 0,
						"is_hot": null,
						"is_promotion": 0,
						"page": 1,
						"pageSize": 20
					}
				 */
      const params = {
        page: json.page,
        pageSize: json.pageSize,
        // supplier_id: parseInt(json.supplier_id),
        // min_price: this.toFen(json.price_form),
        // max_price: this.toFen(json.price_to),
        /* profit_form: this.toFen(json.profit_form),
					profit_to: this.toFen(json.profit_to), */
        // origin_rate:{
        // 	from: this.toFen(json.profit_form),
        // 	to: this.toFen(json.profit_to)
        // }
      }
      if (json.profit_form) {
        params.origin_rate.from = this.toFen(json.profit_form)
      }
      if (json.profit_to) {
        params.origin_rate.to = this.toFen(json.profit_to)
      }
      if (this.searchValue) {
        params.title = this.searchValue
      }
      if (json.sort_by) {
        params.sort_by = json.sort_by
      }
      if (json.price_to) {
        params.max_price = this.toFen(json.price_to)
      }
      if (json.price_form) {
        params.min_price = this.toFen(json.price_form)
      }
      if (json.category1_id) {
        params.category1_id = parseInt(json.category1_id)
      }
      if (json.category2_id) {
        params.category2_id = parseInt(json.category2_id)
      }
      if (json.category3_id) {
        params.category3_id = parseInt(json.category3_id)
      }
      this.post(apiUrl, params, true).then(data => {
        /* for (let i = 0; i < data.data.list.length; i++) {
          // 分转元
          data.data.list[i].price = this.toYuan(data.data.list[i].price)
          data.data.list[i].origin_price = this.toYuan(
            data.data.list[i].origin_price,
          )
        } */
        this.list2 = data.data
        if (this.list2.page < this.list2.total / this.list2.pageSize) {
          this.$store.commit('upHomeNoMore', false)
          this.$store.commit('upSearchGoods', {
            page: this.$store.state.searchGoods.page + 1,
          })
        } else {
          this.$store.commit('upHomeNoMore', true)
        }
        if (stair == 'refresh') {
          // 只有在下拉刷新的时候数组才会一直叠加
          this.list = [...this.list, ...data.data.list]
        } else {
          this.list = data.data.list
        }
        console.log('this.searchValue 列表页kk', this.searchValue)
      })
    },
    listByCollectionIdLogin(stair) {
      // 专辑商品列表
      let url = '/api/product/listByCollectionId'
      let method = 'post'
      this.user = uni.getStorageSync('user')
      if (this.user) {
        url = '/api/product/listByCollectionIdLoginNew'
      }
      const params = {
        page: this.albumParameters.page,
        pageSize: this.albumParameters.pageSize,
        collection_id: parseInt(this.albumParameters.albumId),
      }
      this[method](url, params, true).then(data => {
        /* for (let i = 0; i < data.data.list.length; i++) {
          // 分转元
          data.data.list[i].price = this.toYuan(data.data.list[i].price)
          data.data.list[i].origin_price = this.toYuan(
            data.data.list[i].origin_price,
          )
        } */
        this.list3 = data.data
        if (this.list3.page < this.list3.total / this.list3.pageSize) {
          // 判断是不是最后一页
          this.$store.commit('upHomeNoMore', false)
          this.albumParameters.page += 1
        } else {
          this.$store.commit('upHomeNoMore', true)
        }
        if (stair == 'refresh') {
          // 只有在下拉刷新的时候数组才会一直叠加
          this.list = [...this.list, ...data.data.list]
        } else {
          this.list = data.data.list
        }
      })
    },
    onReachBottom() {
      // 下拉刷新
      if (this.checkNull(this.albumParameters.albumId)) {
        // 专辑商品进入
        if (!this.$store.state.homeNoMore) {
          this.listByCollectionIdLogin('refresh')
        }
      } else {
        // 普通商品进入
        if (!this.$store.state.homeNoMore) {
          this.merchandiseNews('refresh')
        }
      }
    },
    replacementPriceFilter() {
      // 重置价格筛选
      this.$store.commit('upSearchGoods', {
        price_to: '',
        price_form: '',
        profit_to: '',
        profit_form: '',
      })
    },
  },
}
</script>

<style></style>

