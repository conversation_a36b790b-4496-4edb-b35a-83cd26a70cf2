﻿<template>
  <view class="material-box">
    <u--form :model="fromData" :rules="rules" ref="fromData">
      <view class="form-box">
        <u-form-item
          label="素材标题"
          labelWidth="150rpx"
          :required="true"
          prop="title"
          ref="title"
          borderBottom
        >
          <u--input
            v-model="fromData.title"
            placeholder="请输入"
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="素材分组"
          labelWidth="150rpx"
          prop="certificate_type"
          ref="certificate_type"
          borderBottom
        >
          <view class="f fac fjsb w100" @click="groupShowFun">
            <view class="input-view-name1" v-if="group.name === '请选择'">
              {{ group.name }}
            </view>
            <view class="input-view-name" v-else>{{ group.name }}</view>
            <view
              class="iconfont icon-member_right icon_aaa"
              style="color: #aaaab3"
            ></view>
          </view>
        </u-form-item>
        <u-form-item
          label="素材文案"
          labelWidth="150rpx"
          prop="content"
          ref="content"
          borderBottom
        >
          <u--textarea
            height="150rpx"
            v-model="fromData.content"
            placeholder="请输入"
            maxlength="175"
            border="none"
          ></u--textarea>
        </u-form-item>
        <u-form-item
          label="图片"
          prop="cover_url"
          ref="cover_url"
          labelWidth="150rpx"
        ></u-form-item>
        <view class="hint">支持上传多张图片，图片限制在10M以内</view>
        <u-upload
          :fileList="cover_url"
          @afterRead="afterRead"
          :previewFullImage="true"
          @delete="deletePic"
          maxSize="10485760"
          width="190rpx"
          height="190rpx"
          uploadIcon="plus"
          name="cover_url"
          :maxCount="99"
        ></u-upload>
        <u-line margin="25rpx 0 0 0"></u-line>
        <u-form-item
          label="上传视频"
          prop="video_url"
          ref="video_url"
          labelWidth="150rpx"
        ></u-form-item>
        <view class="hint">建议视频大小不超过20M</view>
        <view
          style="width: 280rpx; height: 250rpx; position: relative"
          v-if="fromData.video_url"
        >
          <video
            controls="controls"
            style="width: 100%; height: 100%; border-radius: 16rpx"
            :src="fromData.video_url"
          ></video>
          <view
            class="closebox f fac fjc"
            @click="deletePic({ name: 'video_url', index: 0 })"
          >
            <u-icon name="close" size="22" color="#fff"></u-icon>
          </view>
        </view>
        <u-upload
          v-else
          width="280rpx"
          height="210rpx"
          accept="video"
          maxCount="1"
          maxSize="20971520"
          name="video_url"
          :multiple="true"
          :fileList="video_url"
          :previewImage="true"
          :previewFullImage="true"
          @afterRead="afterRead"
          @delete="deletePic"
        >
          <view class="qr-code-view">
            <u-icon name="plus"></u-icon>
          </view>
        </u-upload>
      </view>
      <!-- 选择商品 -->
      <view class="form-box mt_20">
        <u-form-item
          label="选择商品"
          labelWidth="150rpx"
          prop="product_id"
          ref="product_id"
        >
          <view class="f fac fjsb w100" @click="productShowFun">
            <view class="input-view-name1">请选择</view>
            <view
              class="iconfont icon-member_right icon_aaa"
              style="color: #aaaab3"
            ></view>
          </view>
        </u-form-item>
        <view class="product f fac fjsb" v-if="selectProduct.id">
          <view class="f fac">
            <image
              :src="selectProduct.thumb"
              mode="widthFix"
              class="product-img"
            />
            <view>
              <view class="product-title">{{ selectProduct.title }}</view>
              <view class="product-num">
                ￥
                <span class="product-num2">
                  {{ toYuan(selectProduct.price) }}
                </span>
              </view>
            </view>
          </view>
          <view class="product-but mr_20" style="width: 112rpx; height: 56rpx">
            <u-button
              customStyle="height: 56rpx;font-size:24rpx"
              type="primary"
              color="#AAAAB3"
              shape="circle"
              :plain="true"
              text="更换"
              size="small"
              @click="productShowFun"
            ></u-button>
          </view>
        </view>
      </view>
    </u--form>
    <!-- 素材分组 -->
    <u-popup :show="group.show" :round="30" mode="bottom" @close="groupClose">
      <view class="classify">
        <view class="classify_title d-bf">
          <view class="c-b5 font_size13"></view>
          <view class="c-20 font_size17">请选择</view>
          <view class="font_size13 c-orange" @click="groupClose">
            <u-icon name="close" size="26"></u-icon>
          </view>
        </view>
        <view class="classify_serch">
          <view class="search">
            <u-search
              height="62rpx"
              searchIconSize="40"
              :showAction="true"
              actionText="搜索"
              :animation="false"
              v-model="keyword"
              @search="getGroup"
              @custom="getGroup"
            ></u-search>
          </view>
        </view>
        <view class="classify_content">
          <template v-for="item in group.list">
            <view
              class="option f fjsb fac"
              :key="item.id"
              @click="groupChange(item)"
            >
              <view>{{ item.title }}</view>
              <view
                v-if="item.id === group.id"
                class="iconfont icon-all_select_choose c-orange"
              ></view>
            </view>
          </template>
        </view>
      </view>
    </u-popup>
    <!-- 选择商品 -->
    <productPopup ref="productPopup" @affirm="affirm"></productPopup>
    <view class="but-blue" v-if="brand">
      <u-button
        class="custom-style"
        customStyle="height: 80rpx;font-size:30rpx; "
        color="#2658fa"
        shape="circle"
        text="确认发布"
        @click="popupfun"
      ></u-button>
    </view>
  </view>
</template>

<script>
export default {
    data() {
    return {
      fromData: {
        title: '',
        group_id: '',
        content: '',
        img_url: '',
        video_url: '',
        product_id: '',
      },
      rules: {
        title: [
          {
            type: 'string',
            required: true,
            message: '请输入素材标题',
            trigger: ['blur'],
          },
        ],
      },
      group: {
        show: false,
        list: [],
        id: null,
        name: '请选择',
      },
      keyword: '', // 搜索分组关键字
      materialImageList: [], // 素材图片列表
      cover_url: [],
      video_url: [],
      selectProduct: {},
      brand: true,
    }
  },
  onLoad() {
    this.getGroup()
  },
  methods: {
    // 获取分组
    async getGroup() {
      let res = await this.post('/api/material/center/group', {
        title: this.keyword,
      })
      if (res.code === 0) {
        this.group.list = res.data
      }
    },
    // 打开素材分组
    groupShowFun() {
      this.group.show = true
      if (this.group.name !== '请选择') {
        let res = this.group.list.find(item => item.title === this.group.name)
        this.group.id = res.id
      }
    },
    // 选择素材分组
    groupChange(item) {
      this.group.id = item.id
      this.group.name = item.title
      this.fromData.group_id = parseInt(this.group.id)
      this.$refs.fromData.validateField('group_id')
      this.groupClose()
    },
    // 关闭素材分组
    groupClose() {
      this.group.show = false
    },
    // 上传图片成功
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      lists[0].thumb = lists[0].url
      let file = `${event.name}`
      let fileListLen = this[file].length
      lists.map(item => {
        this[file].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url, file)
        let item = this[file][fileListLen]
        this[file].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result,
          }),
        )
        fileListLen++
      }
    },
    uploadFilePromise(url, file) {
      return new Promise((resolve, reject) => {
        const a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name: 'file',
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
              const data = JSON.parse(res.data)
              switch (file) {
                case 'cover_url':
                  this.materialImageList.push(data.data.file.url)
                  break
                case 'video_url':
                  this.fromData.video_url = data.data.file.url
                  break
                default:
                  break
              }
            }, 1000)
          },
        })
      })
    },
    // 删除图片
    deletePic(event) {
      this[event.name].splice(event.index, 1)
      switch (event.name) {
        case 'cover_url':
          this.materialImageList.splice(event.index, 1)
          break
        case 'video_url':
          this.fromData.video_url = ''
          break
        default:
          break
      }
    },
    // 打开商品
    productShowFun() {
      this.$refs.productPopup.info(
        this.selectProduct.id ? this.selectProduct.id : null,
      )
    },
    // 选中商品
    affirm(selectProduct) {
      this.selectProduct = selectProduct
      if (this.selectProduct.id) {
        this.fromData.product_id = parseInt(this.selectProduct.id)
      }
    },
    // 发布商品
    async popupfun() {
      this.$refs.fromData.validate()
      if (!this.fromData.title) {
        this.toast('请输入素材标题')
        return
      }
      this.fromData.img_url = this.materialImageList.join(',')
      const data = {
        title: this.fromData.title,
        content: this.fromData.content,
        group_id: parseInt(this.fromData.group_id),
        img_url: this.fromData.img_url,
        video_url: this.fromData.video_url,
        product_id: parseInt(this.fromData.product_id),
        user_id: uni.getStorageSync('user').id,
      }
      const res = await this.post('/api/material/self/create', data)
      if (res.code === 0) {
        this.toast(res.msg)
        uni.navigateBack({ delta: 1 })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.material-box {
  width: 702rpx;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
  padding-bottom: 150rpx;
  .form-box {
    background: #ffffff;
    border-radius: 16rpx;
    width: 702rpx;
    padding: 14rpx 32rpx;
    box-sizing: border-box;
  }
}
.classify {
  padding: 40rpx 30rpx 30rpx 30rpx;
  .classify_serch {
    margin-top: 36rpx;
    .search {
      width: 702rpx;
      height: 62rapx;
    }
  }
  .classify_content {
    margin-top: 30rpx;
    height: 600rpx;
    overflow-y: scroll;
    .option {
      height: 103rpx;
      border-bottom: 1px solid #f0f0f1;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}
.qr-code-view {
  min-width: 210rpx;
  min-height: 210rpx;
  max-width: 210rpx;
  max-height: 210rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  background-color: #f8f8f8;

  ::v-deep .u-icon .uicon-plus {
    color: #b4b4b4 !important;
    font-size: 40rpx !important;
  }
}
.but-blue {
  padding: 20rpx 24rpx;
  width: 100%;
  height: 120rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  position: fixed;
  left: 0;
  bottom: 0;
}

.but-blue ::v-deep .u-button__text {
  color: #ffffff;
  font-weight: 500;
  font-size: 30rpx;
}
::v-deep .u-upload__success {
  display: none;
}
::v-deep .u-upload__deletable {
  height: 40rpx;
  width: 40rpx;
  border-radius: 0rpx 16rpx 0rpx 16rpx;
  box-sizing: border-box;
  background-color: rgba($color: #000000, $alpha: 0.5);
  .u-upload__deletable__icon {
    position: relative;
    top: -1px;

    .u-icon__icon {
      span {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }
}
.closebox {
  width: 40rpx;
  height: 36rpx;
  background: rgba($color: #000, $alpha: 0.5);
  border-radius: 0rpx 16rpx 0rpx 16rpx;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
}
.product {
  width: 654rpx;
  height: 140rpx;
  background: #f9f9f9;
  border-radius: 20rpx;
  margin: 0 auto;

  .product-img {
    min-width: 100rpx;
    max-width: 100rpx;
    min-height: 100rpx;
    max-height: 100rpx;
    border-radius: 16rpx;
    margin-left: 20rpx;
    margin-right: 12rpx;
  }

  .product-title {
    color: #00001c;
    font-size: 28rpx;
    font-weight: 500;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .product-num {
    font-weight: 500;
    font-size: 24rpx;
    color: #f15353;
    margin-top: 24rpx;
  }

  .product-num2 {
    font-weight: 500;
    font-size: 28rpx;
    color: #f15353;
  }
}
.product-but ::v-deep .u-button {
  height: 56rpx;
  width: 112rpx;
}
.hint {
  font-weight: 400;
  font-size: 24rpx;
  color: #aaaab3;
  margin-top: -15rpx;
  margin-bottom: 24rpx;
}
.w100 {
  width: 100%;
}
.input-view-name {
  font-size: 30rpx;
}
.input-view-name1 {
  font-size: 30rpx;
  color: rgb(192, 196, 204);
}
</style>

