﻿<template>
		<view class="order_content">
			<u-grid
				:border="false"
				col="5"
			>
			<u-grid-item
							v-for="(listItem,listIndex) in orderList"
							:key="listIndex"
							@click="navTo(listItem.navigation)"
			>	<view class="num">
					<u-badge :type="type" max="99" :value="listItem.num"></u-badge>
				</view>
				
				<view class="iconfont" :class="listItem.nameClass"></view>
				<text class="grid-text font_size12">{{listItem.title}}</text>
			</u-grid-item>
			</u-grid>
		</view>
</template>

<script>
	export default {
		props:{
			orderList:{
				type:Array,
				default:[]
			}
		},
		name: 'myOrder',
		data() {
			return {
				type:"error",
			}
		},
		onShow() {
			
		},
		methods: {
		}
	}
</script>

<style lang="scss">
	.order_content {
		.iconfont {
			font-size: 48rpx;
			margin: 42rpx 0 14rpx 0;
		}
		.num {
			position: absolute;
			left: 56%;
			top:40rpx;
		}
		
	}
</style>

