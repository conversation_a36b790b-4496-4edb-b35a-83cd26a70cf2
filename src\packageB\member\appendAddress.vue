﻿<!-- 新增地址 -->
<template>
	<view>
		<view class="mb20"></view>
		<view class="addForm">
			<u--form
							labelPosition="left"
							:model="addForm"
							:rules="appendRules"
							ref="addForm"
					>
						<u-form-item
								label="姓名:"
								prop="realname"
								borderBottom
								ref="realname"
								labelWidth="176rpx"
								style="padding:0rpx 32rpx;"
						>
							<u--input
									v-model="addForm.realname"
									placeholder="请输入收件人"
									border="none"
							></u--input>
						</u-form-item>
						<u-form-item
								label="联系电话:"
								prop="mobile"
								borderBottom
								ref="mobile"
								labelWidth="176rpx"
								style="padding:0rpx 32rpx;"
						>
							<u--input
									v-model="addForm.mobile"
									placeholder="请输入联系电话"
									border="none"
							></u--input>
						</u-form-item>
						<u-form-item
								label="设置默认地址"
								prop="is_default"
								borderBottom
								ref="is_default"
								labelWidth="240rpx"
								style="padding:0rpx 32rpx;"
						>
						<u-switch v-model="addForm.is_default" @change="defaultChange" size="50"  activeColor="#f56c6c" ></u-switch>
						</u-form-item>
						<u-form-item
								label="所在地区:"
								prop="province"
								borderBottom
								ref="province"
								labelWidth="240rpx"
								style="padding:0rpx 32rpx;"
								
						>
							<u-cell-group :border="false">
								<u-cell @click="openDateLwtbtn" :border="false" :value="addressName?addressName:'请选择地区'" :isLink="true"></u-cell>
							</u-cell-group>
						</u-form-item>
						<u-form-item
								label="街道:"
								prop="town"
								borderBottom
								ref="town"
								labelWidth="240rpx"
								style="padding:0rpx 32rpx;"
								v-if="streetItemShow"
						>
							<u-cell-group :border="false">
								<u-cell @click="streetBtn" :border="false" :value="addForm.town?addForm.town:'请选择街道'" :isLink="true"></u-cell>
							</u-cell-group>
						</u-form-item>
						<u-form-item
								prop="detail"
								style="padding:0rpx 32rpx;"
								class="area">
							<textarea placeholder="请输入详细地址" v-model.trim="addForm.detail" placeholderStyle="color: #ccc;" style="height:142rpx;"></textarea>
						</u-form-item>
					</u--form>
		</view>
		
		<view class="content03 d-cc" @click="$clicks(addFormBtn)" >
			<view class="iconfont icon-customform_add addColor"></view>
			<view class="new-add" >保存</view>
		</view>
		
		<!-- 选择地址的UI组件 -->
		<address-popup
			:addressShow="addressShow"
			@_closeDateLw="closeDateLw"
			@AddressSetOn="addressSetOn"
		>
		</address-popup>

		
		<!-- 选择街道组件 -->
		
		<street-popup
			v-if="newAddNameShow"
			:streetObj="streetObj"
			@closeStreet="closeStreet"
			@streetConfirm="streetConfirm">
		</street-popup>
		
	</view>
</template>

<script>
			export default {
				data() {
			return {
				addressName:'',
				addressShow:false,
				streetItemShow:false, // 显示街道的item
				streetObj:{ //传递给街道组件
					show:false,
					countyId:0
				},
				newAddNameShow:false,
				addForm:{
					realname:'',
					mobile:'',
					is_default: false,
					province:'',
					province_id:0,
					county:'',
					county_id:0,
					city:'',
					city_id:'',
					town:'',
					town_id:0,
					detail:''
				},
				value:false,
				appendRules: {
					'realname': [
						{
							type: 'string',
							required: true,
							message: '请输入收件人姓名',
							trigger: ['blur']
						},
						{
							min: 2,
							max: 15,
							message: '长度在2-15个字符之间',
							trigger: ['blur']
						},
					],
					'mobile':[
						{
							type: 'string',
							required: true,
							message: '请输入联系电话',
							trigger: ['blur']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['blur'],
						}
					],
					'province': {
						type: 'string',
						required: true,
						message: '请选择地区',
						trigger: ['blur']
					},
					'town': {
						type: 'string',
						required: true,
						message: '请选择街道',
						trigger: ['blur']
					},
					'detail': {
						type: 'string',
						required: true,
						message: '请输入详细地址',
						trigger: ['blur']
					},
					
				},
			}
		},
		onLoad(options) {
		},
		onShow() {
			// this._getProvinceData();
			
		},
		watch:{
				addressName: {
					handler(newName, oldName) {
						if(newName) {
							this.newAddNameShow = true;
						}
					},
					immediate: true,
					deep:true
				},
		},
		methods: {
			
			// 关闭选择收货地址
			closeDateLw(cancel) {
				this.addressShow = !cancel;
				this.newAddNameShow = true;
			},
			addressSetOn(...query) {
				console.log(query);
				this.addressName = query[1];
				this.addressShow = false;
				({  //解构赋值
					city: this.addForm.city,
					city_id: this.addForm.city_id,
					county: this.addForm.county,
					county_id: this.addForm.county_id,
					province: this.addForm.province,
					province_id: this.addForm.province_id,
				} = query[0]);
				if(this.addForm.county_id) {
					this.streetObj.countyId = this.addForm.county_id;
					this.streetItemShow = true;
					console.log(this.streetObj.countyId);
				}
				this.newAddNameShow = true;
				this.$refs.addForm.validateField('province') //重新校验一次
			},
			streetBtn() {
				this.streetObj.show = true;
			},
			closeStreet(show) {
				this.streetObj.show = false;
			},
			streetConfirm(street) {  //获取街道地址
				({town:this.addForm.town,town_id:this.addForm.town_id} = street);
				this.$refs.addForm.validateField('town') //重新校验一次
			},
			openDateLwtbtn() {
				this.addressShow = true;
				this.newAddNameShow =false;
				this.addForm.town = '';
			},
			defaultChange(e) {
				console.log(e);
				this.addForm.is_default = e;
			},
			addFormBtn() {
				this.addForm.detail = this.addForm.detail.replace(/\n/g,'')
				this.$refs.addForm.validate().then(res => {
					this.post('/api/address/add',this.addForm).then((res) => {
						console.log(res);
						if(res.code === 0) {
							let data = res.data;
							console.log(data);
							uni.$u.toast('填写成功')
							// this.addForm = {};
							this.$refs.addForm.resetFields();
							this.addressName = '';
							this.addForm.town = '';
							setTimeout(() => {
								this.backRefresh();
							},1000);
							
						} else {
							this.toast(res.msg);
						}
					}).catch((Error) => {
						console.log(Error);
					})
				}).catch(errors => {
					uni.$u.toast('填写错误')
				})
			}

		}
	}
</script>

<style scoped>
	.addForm ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-end;
	}
	.addForm .area ::v-deep .u-form-item__body__right .u-form-item__body__right__content__slot {
		justify-content: flex-start;
	}
	/*#ifdef MP-WEIXIN*/
	.addForm::v-deep .u-form-item {
		padding:0rpx 32rpx;
	}
	/*#endif*/
	
/* 	.wrapper >>> .uni-swiper-slide-frame uni-swiper-item {
		display: flex;
		flex-direction: row;
	} */
</style>
<style lang="scss" scoped>
	.addForm {
		background-color: #fff;
		.textarea {
			margin: 20rpx 32rpx;
		}
	}
	.content03 {
		width: 100%;
		height: 90rpx;
		background-color: #f15353;
		position: fixed;
		bottom: 0;
		z-index: 99;
		.new-add {
			font-size: 28rpx;
			color: #fff;
			margin-left: 20rpx;
		}
		.addColor {
			color: #fff;
		}
	}
	
	/*选择地区*/
	/*地址选择器*/
	/*--------------------------------*/
	.btn-area {
		padding: 0rpx 20rpx;
	}
	
	.dateBe {
		position: fixed;
		bottom: 0rpx;
		left: -5rpx;
		width: 760rpx;
		padding: 0rpx 5rpx;
		box-sizing: border-box;
		z-index: 11000;
		font-size: 28rpx;
		border-top: 1rpx solid #d9d9d9;
		opacity: 0;
		transform: translate(-750rpx, 0rpx);
	}
	
	.dateBe.true {
		opacity: 1;
		transform: translate(0rpx, 0rpx);
	}
	
	.dateBe .head {
		display: flex;
		flex-flow: nowrap;
		padding: 0rpx 30rpx;
		line-height: 80rpx;
		border-bottom: 1rpx solid #d9d9d9;
		background: #f8f8f8;
	}
	
	.dateBe .head .ll {
		flex: 1;
	}
	
	.dateBe .head .rr {
		text-align: right;
		flex: 1;
	}
	
	.dateBe .main {
		background: #f8f8f8;
	}
	
	.dateBe .main view {
		text-align: center;
	}
	

	
</style>

