﻿<!-- 分类tabbar -->
<template>
  <view class="classify">
    <!-- 搜索框 -->
    <u-sticky>
      <view
        style="height: 83rpx; background-color: #ffffff"
        class="pt_16 pb_16 pr_30 pl_30"
      >
        <view class="d-cc searchCss" @click="navTo('/packageA/search/search')">
          <u-icon name="search" size="35"></u-icon
          ><text>请输入商品名称搜索</text>
        </view>
      </view>
    </u-sticky>
    <!--  -->
    <!--商品分类-->
    <my-classify-module ref="classifyModule"></my-classify-module>
    <my-tabbar ref="myTabBar"></my-tabbar>
  </view>
</template>

<script>
// #ifdef H5
import myClassifyModule from '@/my-components/components/myClassifyModule.vue'
import myTabbar from '@/my-components/components/myTabbar.vue'
// #endif

export default {
  // #ifdef H5
  components: {
    'my-classify-module': myClassifyModule,
    'my-tabbar': myTabbar
  },
  // #endif
    data() {
    return {
      classifyViewIsShow: false,
    };
  },
  onShow() {
    this.$nextTick(()=>{
      this.$refs.myTabBar.tabBarActive = uni.getStorageSync('tabBarActive')
      this.$refs.classifyModule.user = uni.getStorageSync("user")
    })
  },
  onHide() {
  },
  onLoad() {},
  methods: {},
  onReachBottom() {
	
  },
};
</script>
<style scoped>
.classify ::v-deep .u-sticky {
  top: 0rpx !important;
}
</style>
<style>
.searchCss {
  background-color: #f2f2f2;
  width: 690rpx;
  height: 70rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #8c8c8c;
}

.wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #cccccc;
  justify-content: center;
  align-items: center;
}
</style>

