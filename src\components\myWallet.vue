﻿<!-- 我的钱包组件 -->
<template>
	<view class="my_wallet mt-20 mb-20 b-r-10 d-f">
		<view class="wallet_left ">
			<scroll-view class="scroll-view_H "
				scroll-x="true" 
		
				scroll-left="120" 
				:scroll-left="scroll_left" 
				scroll-with-animation="true"
				>
				<view class="d-bf">
					<block v-for="(item,index) in walletList" :key="index">
							<view class="wallet_box d-cc-c f-bold">
								<view class="mb-10 font_size13">{{item.num.toFixed(2)}}</view>
								<view class="c-gray">{{item.name}}</view>
							</view>
					</block>
				</view>
			</scroll-view>
		</view>
		<view class="wallet_right d-cc-c">
				<view class="mb-10 iconfont icon-fontclass-shouru"></view>
				<view class="c-gray">我的收入</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scroll_left:0  ,// 精选臻品 nav 栏的位置
				walletList: [
					{
						num: 345.00,
						name: 'myWallet'
					},
					{
						num: 10200.00,
						name: 'myWallet'
					},
					{
						num: 345.00,
						name: 'myWallet'
					},
					{
						num:10200.00,
						name: 'myWallet'
					},
					{
						num:345.00,
						name: 'myWallet'
					}
				]
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss">
	.my_wallet {
		background-color: #fff;
		height: 130rpx;
		.wallet_left {
			width:76%;
			box-sizing: border-box;
			padding: 38rpx 0 30rpx 52rpx;
			.scroll-view_H {
				white-space: nowrap;
				.wallet_box {
					margin-right: 86rpx;
				}
			}
		}
		.wallet_right {
			width: 24%;
			box-sizing: border-box;
			padding: 12rpx 0;
			position: relative;
			::after {
				content: " ";
				position: absolute;
				left: 0;
				top: 0;
				width: 2px;
				bottom: 0;
				background-image: radial-gradient(#d4d4d4 5%,#fff 80%,#fff 0);
			}
			.iconfont {
				font-size: 26px;
				color: #f15353;
			}
		}
	}
</style>

