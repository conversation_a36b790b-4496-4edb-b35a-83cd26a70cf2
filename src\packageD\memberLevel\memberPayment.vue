﻿<!-- 会员续费/升级支付页 -->
<template>
  <view>
    <!--支付详情 -->
    <view class="pay-box">
      <view class="pay-money">
        <view class="top">支付金额:</view>
        <view class="mid">￥{{ amount || 0.0 }}</view>
        <view class="bottom">支付流水号:{{ pay_sn }}</view>
      </view>
    </view>
    <view class="mb40"></view>
    <view id="payBtnList">
      <view class="title d-cf">
        <view class="line"></view>
        <view>支付方式</view>
      </view>
      <view class="mb_96">
        <u-radio-group
          v-model="payRadioList"
          placement="column"
          v-for="(item, index) in payList"
          :key="index"
        >
          <view class="mod_btns d-bf">
            <view class="mod_btn d-cf">
              <text class="iconfont icon-pay_yue" v-if="item.code === 2"></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.code === 1"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 5"
              ></text>
              <text
                class="iconfont icon-pay_default"
                v-if="item.code === 11"
                style="font-size: 60rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 12"
                style="font-size: 52rpx"
              ></text>
              <text
                class="iconfont icon-pay_wechat"
                v-if="item.code === 10"
                style="font-size: 60rpx; margin-right: 22rpx"
              ></text>
              <view>
                <text>{{ item.name }}</text>
                <view v-if="item.code === 2">
                  可用:
                  <text class="c-orange">
                    {{ balanceTypeOne.purchasing_balance || 0.0 }}
                  </text>
                  元
                </view>
                <view v-if="item.code === 1">
                  可用:
                  <text class="c-orange">
                    {{ balanceTypeOne.converge_balance || 0.0 }}
                  </text>
                  元
                </view>
              </view>
            </view>
            <u-radio
              @change="radioChange(item.code)"
              size="40"
              activeColor="#f15353"
              :name="item.name"
            ></u-radio>
          </view>
        </u-radio-group>
      </view>
    </view>
    <view class="newSubTn" @click="$clicks(payment)">立即支付</view>
    <u-modal
      :show="payShow"
      :showCancelButton="true"
      :asyncClose="true"
      :content="content"
      @confirm="payConfirm"
      @cancel="paycancel"
      ref="uModal"
    >
      <view class="slot-content">
        <rich-text :nodes="content"></rich-text>
      </view>
    </u-modal>
    <payment-popup
      :paymentShow="paymentShow"
      :payImg="payImg"
      @payClose="payClose"
    ></payment-popup>
  </view>
</template>
<script>
export default {
  name: 'memberPayment',
    data() {
    return {
      // 基本案列数据
      payList: [],
      orderIds: [],
      balanceList: [], // 余额数据
      balanceTypeOne: {
        // 余额显示
        type: null,
        purchasing_balance: 0, // 站内余额
        converge_balance: 0, // 汇聚余额
      },
      pollingState: '', // 轮询
      payShow: false,
      paymentShow: false,
      urlResult: '',
      payImg: '',
      // u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
      payRadioList: 2,
      amount: 0,
      pay_info_id: 0,
      pay_type: 0,
      pay_sn: '',
      content: '是否支付',
      title: '支付',
      currentPlatform: '', // 当前平台
      pageSourse: '',
    }
  },
  onShow() {
    const orderIds = uni.getStorageSync('orderIds')
    console.log('orderIds 111', uni.getStorageSync('orderIds'))
    if ((orderIds ?? '') !== '') {
      this.orderIds = orderIds
    }
    const pageSourse = uni.getStorageSync('pageSourse')
    if ((pageSourse ?? '') !== '') {
      this.pageSourse = pageSourse
    }
    console.log('this.pageSourse', this.pageSourse)
    this.cashier()
    this.getUserBalance()
  },
  onLoad() {
    this.payListData()
  },
  methods: {
    paycancel() {
      this.payShow = false
    },
    payConfirm() {},
    radioChange(code) {
      this.pay_type = code
    },
    payClose() {
      this.paymentShow = false
      clearInterval(this.pollingState)
    },
    cashier() {
      uni.removeStorageSync('redirect_page')
      this.post(
        '/api/trade/cashier',
        {
          order_ids: this.orderIds,
        },
        true,
      )
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            // this.payList = data;
            this.amount = this.toYuan(data.pay_info.amount)
            this.pay_info_id = data.pay_info.id
            this.pay_sn = data.pay_info.pay_sn
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    wechatPayBlend(pay_type) {
      // 微信小程序支付/h5/公众号支付
      const json = {
        pay_type,
        pay_info_id: this.pay_info_id,
      }

      // #ifdef H5
      console.log(this.obtainingDeviceInformation(['platform']))
      if (this.obtainingDeviceInformation(['platform']).platform == 'ios') {
        json.type = 'iOS'
      } else if (
        this.obtainingDeviceInformation(['platform']).platform == 'android'
      ) {
        json.type = 'Android'
      } else {
        json.type = 'Android'
      }
      // #endif
      const that = this
      this.post('/api/finance/wechatPayment', json, true, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            // 支付后的跳转链接
            let jumpUrl
            if (this.pageSourse === 'memberRight') {
              jumpUrl = '/packageA/myOrder/myOrder'
            } else {
              jumpUrl = '/packageD/memberLevel/memberRight'
            }
            let json = {}
            if (pay_type == 11) {
              // 微信H5支付
              window.location.href =
                data.h5_url +
                '&redirect_url=' +
                document.location.protocol +
                '//' +
                // window.location.host +
                // '/h5/?menu%23/packageA/myOrder/myOrder'
                window.location.host +
                '/h5/?menu%23' +
                jumpUrl
              return
            }
            if (pay_type == 12) {
              // 公众号支付
              const pageUrl = window.location.href.replace('#', '?menu#')
              const config = {
                appId: data.pay.appId,
                beta: false,
                debug: false,
                nonceStr: data.pay.nonceStr,
                signature: data.pay.paySign,
                timestamp: data.pay.timeStamp,
                url: pageUrl,
              }
              window.jweixin.config(config)
              json = {
                appId: data.pay.appId,
                nonceStr: data.pay.nonceStr,
                package: data.pay.package,
                timestamp: data.pay.timeStamp,
                paySign: data.pay.paySign,
                signType: data.pay.signType,
              }
              window.jweixin.chooseWXPay({
                appId: json.appId,
                timestamp: json.timestamp, // 支付签名台生成签
                nonceStr: json.nonceStr, // 支付签名随机串，不长于 32 位
                package: json.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
                signType: json.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                paySign: json.paySign, // 支付签名
                success: res => {
                  // 支付成功后的回调函数
                  if (res.errMsg == 'chooseWXPay:ok') {
                    that.toast('支付成功')
                    setTimeout(() => {
                      uni.redirectTo({
                        // url: '/packageA/myOrder/myOrder',
                        url: jumpUrl,
                      })
                    }, 1000)
                  }
                },
                cancel: res => {
                  // 支付取消
                  that.toast('支付取消')
                },
                fail: res => {
                  that.toast('支付失败')
                  setTimeout(() => {
                    uni.redirectTo({
                      // url: '/packageA/myOrder/myOrder',
                      url: jumpUrl,
                    })
                  }, 1000)
                },
              })
              return
            }
            if (pay_type == 10) {
              // 微信小程序支付
              uni.requestPayment({
                provider: 'wxpay',
                timeStamp: data.timeStamp,
                nonceStr: data.nonceStr,
                package: data.package,
                signType: data.signType,
                paySign: data.paySign,
                success: function (res) {
                  that.toast('支付成功')
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/packageA/myOrder/myOrder',
                    })
                  }, 1000)
                },
                fail: function (err) {
                  that.toast('支付失败')
                  setTimeout(() => {
                    uni.redirectTo({
                      // url: '/packageA/myOrder/myOrder',
                      url: jumpUrl,
                    })
                  }, 1000)
                },
              })
            }
          } else {
            that.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    getPayStatus() {
      // 获取支付状态
      this.post('/api/finance/getPayStatus', {
        pay_sn: this.pay_sn,
      }).then(res => {
        if (res.data.pay_status == 1) {
          clearInterval(this.pollingState) // 停止轮询
          this.paymentShow = false
          this.showText('充值成功')
        }
      })
    },
    getUserBalance() {
      // 获取收入
      this.post('/api/finance/getUserBalance', {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.balanceList = data
            for (let i = 0; i < this.balanceList.length; i++) {
              if (this.balanceList[i].type === 1) {
                this.balanceTypeOne.type = this.balanceList[i].type
                this.balanceTypeOne.converge_balance =
                  this.balanceList[i].purchasing_balance // 汇聚余额，要单独取出来
              } else if (this.balanceList[i].type === 2) {
                this.balanceTypeOne.purchasing_balance =
                  this.balanceList[i].purchasing_balance // 站内余额
              }
            }
            this.balanceTypeOne.converge_balance = this.toYuan(
              this.balanceTypeOne.converge_balance,
            )
            this.balanceTypeOne.purchasing_balance = this.toYuan(
              this.balanceTypeOne.purchasing_balance,
            )
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    payListData() {
      this.platformToJudge()
      console.log(this.currentPlatform)
      // 修改支付方式
      let params = {}
      // #ifdef  MP-WEIXIN
      params.platform = 'miniProgram'
      // #endif
      // #ifdef H5
      params.platform = 'h5'
      // #endif
      this.post('/api/payment/getPayment', params, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data.PaymentList
            data.forEach((item, index) => {
              // 不同端显示不同支付方式
              if (
                item.type.indexOf(this.currentPlatform) != -1 ||
                item.type == 'all'
              ) {
                this.payList.push(item)
              }
            })
            // this.pay_type = data[0].code;
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    wechatPay() {
      const amount = this.toFen(this.amount)
      let _payType = null
      // #ifdef  H5
      _payType = 11
      // #endif
      // #ifdef  MP-WEIXIN
      _payType = 4
      // #endif
      const that = this
      if (_payType) {
        this.post(
          '/api/finance/wechatPay',
          {
            amount,
            pay_sn: this.pay_sn,
            pay_type: _payType,
            pay_code: 3001,
          },
          true,
        )
          .then(res => {
            if (res.code === 0) {
              const data = res.data
              if (_payType === 11) {
                // 微信H5支付
                window.location.href =
                  data.rc_Result +
                  '&redirect_url=' +
                  document.location.protocol +
                  '//' +
                  window.location.host +
                  '/h5/?menu%23/packageA/myOrder/myOrder'
                return
              }
              if (_payType === 4) {
                // 微信小程序支付
                const _data = JSON.parse(data.rc_Result)
                uni.requestPayment({
                  provider: 'wxpay',
                  timeStamp: _data.timeStamp,
                  nonceStr: _data.nonceStr,
                  package: _data.package,
                  signType: _data.signType,
                  paySign: _data.paySign,
                  success: function (res) {
                    that.toast('支付成功')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/packageA/myOrder/myOrder',
                      })
                    }, 1000)
                  },
                  fail: function (err) {
                    that.toast('支付失败')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/packageA/myOrder/myOrder',
                      })
                    }, 1000)
                  },
                })
              }
              // this.toast(res.msg);
              /* this.payImg = data.rd_Pic;
							this.urlResult = data.rc_Result;
							let url = data.rc_Result;
							this.paymentShow = true;
							this.pollingState = setInterval(this.getPayStatus, 2000, res.data.r2_OrderNo); //轮询获取充值状态 */
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {
            console.log(Error)
          })
      } else {
        this.toast('系统错误')
      }
    },
    StationPay() {
      const amount = this.toFen(this.amount)
      this.post(
        '/api/finance/balanceDeduction',
        {
          amount,
          pay_info_id: this.pay_info_id,
          pay_type: this.pay_type,
        },
        true,
        true,
      )
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.toast(res.msg)
            if (this.pageSourse === 'memberRight') {
              this.toast(res.msg)
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageD/memberLevel/memberRight',
                })
              }, 1000)
            } else {
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageA/myOrder/myOrder',
                })
              }, 1000)
            }
          } else {
            this.toast(res.msg)
            if (this.pageSourse === 'memberRight') {
              this.toast(res.msg)
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageD/memberLevel/memberRight',
                })
              }, 1000)
            } else {
              setTimeout(() => {
                uni.redirectTo({
                  url: '/packageA/myOrder/myOrder',
                })
              }, 1000)
            }
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    payment() {
      if (this.pay_type == 1 || this.pay_type === 2 || this.pay_type === -3) {
        this.StationPay()
      } else if (this.pay_type === 5) {
        this.wechatPay()
      } else if (this.pay_type === 10) {
        this.wechatPayBlend(this.pay_type)
      } else if (this.pay_type === 12) {
        this.wechatPayBlend(this.pay_type)
      } else if (this.pay_type === 11) {
        this.wechatPayBlend(this.pay_type)
      } else {
        this.toast('请选择支付方式')
      }
    },

    platformToJudge() {
      // 0PC 1H5 2微信小程序 3微信公众号
      // #ifdef H5
      if (window.innerWidth > 768) {
        this.currentPlatform = '0'
      } else {
        this.currentPlatform = '1'
      }
      // #endif

      // #ifdef MP-WEIXIN
      this.currentPlatform = '2'
      // #endif

      if (this.checkWenxin()) {
        this.currentPlatform = '3'
      }
    },
    getPhoneEnv() {
      const u = navigator.userAgent
      const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1 // g
      const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) || this.isApp() // ios终端或者云打包
      const isIpad =
        u.indexOf('Intel Mac OS') > -1 &&
        u.indexOf('Chrome') === -1 &&
        u.indexOf('Safari') === -1 &&
        u.indexOf('Firefox') === -1
      if (isAndroid) {
        return '2'
      } else if (isIOS || isIpad) {
        return '1'
      } else {
        return '3'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.pay-box {
  background: #fff;
  margin: 18rpx 32rpx;

  .pay-money {
    margin: 0 auto;
    padding: 40rpx 0;
    text-align: center;
    border-radius: 10rpx;

    .top {
      color: #999;
      font-size: 12px;
    }

    .mid {
      color: #f76d6d;
      font-size: 48rpx;
      margin: 32rpx 0;
    }

    .bottom {
      font-size: 12px;
      color: #999;
    }
  }
}

#payBtnList {
  margin: 0 32rpx 80rpx 32rpx;
  padding: 24rpx 0;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;

  .title {
    color: #f87070;
    font-size: 28rpx;
    margin-bottom: 32rpx;

    .line {
      height: 28rpx;
      width: 6rpx;
      background-color: #f87070;
      display: inline-block;
      margin-right: 28rpx;
    }
  }

  .mod_btns {
    border-bottom: 1px solid #f4f4f4;
    text-align: left;
    margin: 0 10rpx;
    padding: 28rpx 0 28rpx 18rpx;

    .icon-pay_yue {
      font-size: 56rpx;
      color: #ff7433;
      margin-right: 32rpx;
    }

    .icon-pay_otherpay {
      font-size: 56rpx;
      color: #ffba00;
      margin-right: 32rpx;
    }

    .icon-pay_wechat {
      font-size: 56rpx;
      color: green;
      margin-right: 32rpx;
    }

    .icon-pay_remittance {
      font-size: 56rpx;
      color: #ff692f;
      margin-right: 32rpx;
    }

    .icon-pay_default {
      font-size: 56rpx;
      color: #2f9cff;
      margin-right: 32rpx;
    }

    .icon-pay_alipay {
      color: #29a1f7;
      font-size: 56rpx;
      margin-right: 32rpx;
    }
  }
}

.newSubTn {
  width: 80%;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  position: fixed;
  bottom: 24rpx;
  left: 10%;
  border-radius: 8px;
  background-color: #f15353;
  color: #fff;
}
</style>

