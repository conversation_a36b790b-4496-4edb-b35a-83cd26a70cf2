﻿<!-- 兼容小程序，H5,app的自定义导航栏 -->
<template>
	<view class="uni_topbar" :style="[style]" :class="[fixed ? 'fixed' : '']" >
		<!-- 'padding-top': statusBarH + 'px',customBarH -->
		<view class="d-bf" :style="[{'height': 45 + 'px',  'color': titleTintColor}, bgColor]">
			<!-- 返回 -->
			<view v-if="isBack" @tap="goBack" class="back">
				<slot name="back"></slot>
			</view>
			<slot name="headerL"></slot>
			<!-- 标题 -->
			<!-- #ifndef MP -->
			<view class="flex1" v-if="!search && center"></view>
			<!-- #endif -->
			<view class="uni_title flex1" :class="[center ? 'uni_titleCenter' : '']" :style="[isBack ? {'font-size': '32upx', 'padding-left': '0'} : '']" v-if="!search && title">
				{{title}}
			</view>
			<view class="uni_search flex1" :class="[searchRadius ? 'uni_searchRadius' : '']" v-if="search"> />
				<input class="uni_searchIpt flex1" type="text" placeholder="搜索" placeholder-style="color: rgba(255,255,255,.5);" />
			</view>
			<!-- 右侧 -->
			<view class="uni_headerRight ">
				<slot name="iconfont"></slot>
				<slot name="string"></slot>
				<slot name="image"></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'myHeaderBar',
		props:{
			isBack: { type: [Boolean, String], default: true },
			title: { type: String, default: '' },
			titleTintColor: { type: String, default: '#fff' },
			bgColor: Object,
			center: { type: [Boolean, String], default: false },
			search: { type: [Boolean, String], default: false },
			searchRadius: { type: [Boolean, String], default: false },
			fixed: { type: [Boolean, String], default: false },
		},
		data() {
			return {
				statusBarH: this.statusBar,
				customBarH: this.customBar
			}
		},
		computed:{
			style() {
				let _style = `height: ${this.customBarH}px;`
				return _style
			}
		},
		methods: {
			goBack() {
				let pages = getCurrentPages();
				let page = pages[pages.length -1].route;
				return
				
				// #ifdef H5
				history.back(-1);
				// #endif
				// #ifndef H5
				uni.navigateBack({
					delta: 1,
				})
				// #endif
				
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni_topbar {
		width: 100%;
		height: 66rpx;
		line-height: 66rpx;
		.back {
			margin-left: 20rpx;
		}
		
		.uni_headerRight {
			margin-right: 20rpx;
		}
	}
	.fixed {
		position: fixed;
		top:0;
		z-index: 100;
	}
</style>

