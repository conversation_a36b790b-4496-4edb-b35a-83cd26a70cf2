﻿<template>
  <view>
    <!-- 搜索框与一级 start -->
    <view class="head" :class="{ head: true, 'head-shadow': tapShow }">
      <u-row
        justify="space-between"
        gutter="40"
        customStyle="margin-bottom: 5rpx"
      >
        <u-col span="12">
          <view class="f d-cf mb-15">
            <u-search
              height="70"
              searchIconSize="40"
              color="#666666"
              bgColor="#eff0f1"
              v-model="formData.title"
              placeholder="搜索商品"
              :showAction="false"
              @change="search"
            ></u-search>
            <view
              v-if="categoryTags.id === 'all'"
              class="f ml-25"
              @click="filterType"
            >
              <view class="iconfont icon-a-shaixuan1 font_size16"></view>
              <text class="ml-5 fs-1-5">筛选</text>
            </view>
            <view
              v-if="categoryTags.id !== 'all'"
              class="f ml-25"
              @click="filterType"
            >
              <view
                class="iconfont icon-a-shaixuan1 font_size16"
                style="color: #f51d1d"
              ></view>
              <text class="ml-5 fs-1-5" style="color: #f51d1d">筛选</text>
            </view>
            <view
              class="f ml-25"
              @click="navTo('/packageD/smallShop/productSelection')"
            >
              <view class="iconfont icon-a-xuanpin1 font_size16"></view>
              <view class="ml-5 fs-1-5">选品</view>
            </view>
          </view>
        </u-col>
      </u-row>
      <scroll-view scroll-x>
        <view class="f">
          <u-tag
            v-if="isAll !== 0"
            class="mb-30"
            :text="categoryTags.name"
            shape="circle"
            color="#F51D1D"
            borderColor="#fee8e8"
            bgColor="#fee8e8"
            closable
            closeColor="#F51D1D"
            @close="closeCategoryTags"
          ></u-tag>
          <u-tag
            v-if="isAll === 2 && secondColumnsId"
            class="mb-30"
            :text="secondColumns[0][0].name"
            shape="circle"
            color="#F51D1D"
            borderColor="#fee8e8"
            bgColor="#fee8e8"
            closable
            closeColor="#F51D1D"
            @close="closeCategory2Tags"
          ></u-tag>
          <u-tag
            v-if="isAll === 2 && thirdColumnsId"
            class="mb-30"
            :text="thirdColumns[0][0].name"
            shape="circle"
            color="#F51D1D"
            borderColor="#fee8e8"
            bgColor="#fee8e8"
            closable
            closeColor="#F51D1D"
            @close="closeCategory3Tags"
          ></u-tag>
          <u-tag
            v-if="formData.min_price && formData.max_price"
            class="mb-30"
            :text="formData.min_price + '-' + formData.max_price"
            shape="circle"
            color="#F51D1D"
            borderColor="#fee8e8"
            bgColor="#fee8e8"
            closable
            closeColor="#F51D1D"
            @close="closePriceTags"
          ></u-tag>
          <u-tag
            v-if="formData.profit_form && formData.profit_to"
            class="mb-30"
            :text="formData.profit_form + '%' + '-' + formData.profit_to + '%'"
            shape="circle"
            color="#F51D1D"
            borderColor="#fee8e8"
            bgColor="#fee8e8"
            closable
            closeColor="#F51D1D"
            @close="closeProfitTags"
          ></u-tag>
        </view>
      </scroll-view>
      <!-- <u-row justify="space-between">
        <u-col span="12"> -->
      <!-- 一级分类-tabs start -->
      <!-- <view class="con-tabs">
            <u-tabs
              lineWidth="100rpx"
              lineColor="#f56c6c"
              itemStyle="padding-right: 20rpx; height: 68rpx"
              :list="firstTab"
              :current="firstIndex"
              :scrollable="true"
              :activeStyle="{ color: '#f14e4e' }"
              :inactiveStyle="{ color: '#0c0d0e' }"
              @change="onChangeFirstTab"
            >
              <view slot="right" @click="tapShow = !tapShow">
                <u-icon
                  size="30"
                  bold
                  :name="tapShow ? 'arrow-up' : 'arrow-down'"
                  color="#8F8F8F"
                ></u-icon>
              </view>
            </u-tabs> -->
      <!-- 一级分类全部展开 start -->
      <!-- <transition name="fade">
              <view v-if="tapShow" class="con-drawer">
                <template v-for="(item, index) in firstTab">
                  <view
                    class="btn-2"
                    :key="index"
                    v-bind:class="{ activeSub: isActiveSub === index }"
                    @click="onClickSubBtn(index, item)"
                  >
                    {{ item.name }}
                  </view>
                </template>
              </view>
            </transition> -->
      <!-- 一级分类全部展开 end -->
      <!-- </view> -->
      <!-- 一级分类-tabs end -->
      <!-- </u-col> -->
      <!-- </u-row> -->
    </view>
    <!-- 搜索框与一级 end -->
    <!-- 筛选与分类 start -->
    <view class="con-sift">
      <!-- 营销活动-按钮 start -->
      <view v-show="isAll === 1 && isSalesShow">
        <u-row
          justify="space-between"
          gutter="40"
          customStyle="margin: 20rpx 0 0 0"
        >
          <template v-for="(item, index) in salesBtn">
            <u-col span="3" :key="index">
              <view
                class="btn-1"
                v-bind:class="{ activeSales: isActiveSales === index }"
                @click="onClickSalesBtn(index, item)"
              >
                {{ item }}
              </view>
            </u-col>
          </template>
        </u-row>
      </view>
      <!-- 营销活动-按钮 end -->
      <!-- 一级分类-按钮 start -->
      <!-- <view v-show="isAll === 2">
        <u-row
          justify="space-between"
          gutter="40"
          customStyle="margin-top: 20rpx"
        >
          <u-col span="6">
            <u-button
              size="small"
              :text="secondName"
              @click="onOpenSecondPicker"
            ></u-button>
          </u-col>
          <u-col span="6">
            <u-button
              size="small"
              :text="thirdName"
              @click="onOpenThirdPicker"
            ></u-button>
          </u-col>
        </u-row>
      </view> -->
      <!-- 一级分类-按钮 end -->
      <!-- 二三级分类-picker start -->
      <template>
        <view>
          <u-picker
            keyName="name"
            :show="secondPickerShow"
            :columns="secondColumns"
            @change="onChangePicker"
            @confirm="onConfirmSecondPicker"
            @cancel="secondPickerShow = false"
          ></u-picker>
          <u-picker
            keyName="name"
            :show="thirdPickerShow"
            :columns="thirdColumns"
            @change="onChangePicker"
            @confirm="onConfirmThirdPicker"
            @cancel="thirdPickerShow = false"
          ></u-picker>
        </view>
      </template>
      <!-- 二三级分类-picker end -->
      <!-- 筛选 start -->
      <view class="d-f-c w100">
        <view class="d-f w100">
          <view class="con-sort">
            <SortButtonGroup v-model="sortForm" @change="onChangeSort">
              <SortButton value="price">价格</SortButton>
              <SortButton value="profit">利润</SortButton>
              <SortButton value="profit_rate">利润率</SortButton>
              <SortButton value="sales">销量</SortButton>
            </SortButtonGroup>
          </view>
          <view class="con-btn">
            <view class="iconfont icon-all_delete_1 c-66A3E7 mt-5"></view>
            <view
              class="font_size12 c-8a d-cf fontw500 c-66A3E7 ml-10"
              @click="checkoutDel"
            >
              批量删除
            </view>
          </view>
        </view>
        <!--  -->
      </view>
      <!-- 筛选 end -->
    </view>
    <!-- 筛选与分类 end -->
    <!-- 商品列表 start -->
    <view class="con-list">
      <u-checkbox-group placement="column" @change="onCheckSome">
        <template v-for="(item, index) in productList">
          <view class="relative" :key="item.id">
            <view v-if="isDelete">
              <view
                class="checkbox"
                v-if="checkboxValue.find(i => i === item.id) ? false : true"
              >
                <u-checkbox
                  size="40"
                  iconSize="40"
                  :checked="false"
                  shape="circle"
                  inactiveColor="#8B8B8B"
                  :key="item.id"
                  :name="item.id"
                ></u-checkbox>
              </view>
              <view class="checkbox" v-else>
                <u-checkbox
                  size="40"
                  :checked="true"
                  shape="circle"
                  activeColor="#FE5E56"
                  :key="item.id"
                  :name="item.id"
                ></u-checkbox>
              </view>
            </view>
            <view
              class="p-20 mb_30 bg-white b-r-10 d-f"
              :key="item.id"
              @click="goProductDetail(item.id)"
            >
              <u-image
                width="140rpx"
                height="140rpx"
                :showLoading="true"
                :src="item.image_url"
              >
                <u-icon
                  slot="error"
                  size="40"
                  color="#d0d0d1"
                  name="photo"
                ></u-icon>
              </u-image>
              <view class="ml_20 w63">
                <!-- 标题 start-->
                <view class="d-f">
                  <view class="pr_20" style="width: 100%">
                    <text class="fs-2 ell line2">{{ item.title }}</text>
                    <view class="fs-0 c-8a mt_10">
                      <text class="mr-45">
                        利润率{{ toPercent(item.profit_rate) }}
                      </text>
                      <text class="mr-40">
                        售价￥{{ toYuan(item.shop_price) }}
                      </text>
                      <text>销量{{ item.sales }}</text>
                    </view>
                  </view>
                </view>
                <!-- 标题 end-->
                <view class="d-bf d-cf mt_15 mb-20">
                  <view class="c-orange">
                    <text class="fs-0-5">赚￥</text>
                    <text class="fs-2">{{ toYuan(item.profit) }}</text>
                  </view>
                </view>
                <!-- 按钮 -->
                <view class="f">
                  <!-- 批量改价 start -->
                  <!-- <text
                      class="ml_40 mr_40 c-fa fs-0-5"
                      @click.stop="onOpenEditPopup"
                    >
                      改价
                    </text> -->
                  <!-- 批量改价 end -->
                  <u-button
                    class="c-66A3E7"
                    shape="circle"
                    size="mini"
                    style="
                      border: 1rpx solid #aaaab3;
                      width: 150rpx;
                      height: 56rpx;
                      line-height: 56rpx;
                    "
                    @click="goProductDetail(item.id)"
                  >
                    查看
                  </u-button>
                  <!-- 单个改价 start -->
                  <u-button
                    class="ml-20 mr-20 c-fa"
                    shape="circle"
                    size="mini"
                    style="
                      border: 1rpx solid #aaaab3;
                      width: 150rpx;
                      height: 56rpx;
                      line-height: 56rpx;
                    "
                    @click.native.stop="onOpenPricePopup(item)"
                  >
                    改价
                  </u-button>
                  <!-- 单个改价 end -->
                  <u-button
                    class="c-orange"
                    shape="circle"
                    size="mini"
                    style="
                      border: 1rpx solid #aaaab3;
                      width: 150rpx;
                      height: 56rpx;
                      line-height: 56rpx;
                    "
                    @click.native.stop="onDelete(item.id, index)"
                  >
                    删除
                  </u-button>
                </view>
              </view>
            </view>
          </view>
        </template>
      </u-checkbox-group>
      <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
        <view v-if="isLast && productList.length != 0">暂无更多~</view>
        <view v-if="productList.length == 0">暂无数据~</view>
      </view>
    </view>
    <!-- 商品列表 end -->
    <!-- 商品管理、批量改价 start -->
    <PricePopup
      ref="pricePopup"
      :pricePopupShow="pricePopupShow"
      :editPopupShow="editPopupShow"
      :price="price"
      :product="product"
      :headTitle="headTitle"
      @onOpenEditPopup="onOpenEditPopup"
      @onConfirmPricePopup="onConfirmPricePopup"
      @onConfirmEditPopup="onConfirmEditPopup"
      @onClosePricePopup="onClosePricePopup"
      @onCloseEditPopup="onCloseEditPopup"
    ></PricePopup>
    <!-- 商品管理、批量改价 end -->
    <!-- 批量删除组件 -->
    <view v-if="isDelete" class="del-pop f d-bf">
      <view class="f d-cf ml-25">
        <u-checkbox-group placement="column">
          <u-checkbox
            v-if="chooseAllcheck"
            class="mt-5"
            size="40"
            shape="circle"
            inactiveColor="#8B8B8B"
            activeColor="#FE5E56"
            label="全选"
            labelSize="28"
            labelColor="#00001C"
            @change="allCheckbox"
          ></u-checkbox>
          <u-checkbox
            v-else
            class="mt-5"
            size="40"
            shape="circle"
            activeColor="#FE5E56"
            label="全选"
            labelSize="28"
            labelColor="#00001C"
            @change="allCheckbox"
          ></u-checkbox>
        </u-checkbox-group>
        <text style="color: #807e7e">（已选{{ checkboxValue.length }}）</text>
      </view>
      <!-- #ifdef H5 -->
      <view class="d-bf">
        <u-button shape="circle" style="width: 168rpx" @click="cancelDel">
          取消
        </u-button>
        <u-button
          shape="circle"
          style="width: 168rpx"
          class="ml-25 mr-25"
          type="error"
          @click="onDelete"
          :disabled="checkboxValue.length === 0"
        >
          删除
        </u-button>
      </view>
      <!--  #endif -->
      <!--  #ifdef MP-WEIXIN -->
      <view style="width: 300rpx" class="d-bf">
        <u-button shape="circle"  size="large" @click="cancelDel">取消</u-button>
        <u-button shape="circle" :disabled="checkboxValue.length === 0" size="large" type="error" @click="onDelete">删除</u-button>
      </view>
      <!--  #endif -->
    </view>
    <!-- 筛选弹出层 -->
    <view v-if="filterPropShow" class="filter-pop">
      <view class="shop-filter">
        <!-- 商品类型 -->
        <view class="ml-25">
          <view class="mt-40" style="color: #807e7e">商品类型:</view>
          <scroll-view class="scroll-filter" scroll-y>
            <view class="d-f d-wrap">
              <view
                class="d-f d-wrap"
                v-for="(item, index) in firstTab"
                :key="index"
              >
                <view
                  class="btn-2"
                  :key="index"
                  v-bind:class="{ activeSub: isActiveSub === index }"
                  @click="onClickSubBtn(index, item)"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 商品分类 -->
        <view v-show="isAll === 2">
          <view class="mt-40 ml-25" style="color: #807e7e">商品分类:</view>
          <!-- 一级分类-按钮 start -->
          <view class="f d-cf">
            <uni-data-select
              class="shop-category"
              :localdata="secondColumns"
              v-model="secondColumnsId"
              @change="chooseSecondColumnsId"
              :clear="false"
              emptyTips="暂无数据"
            ></uni-data-select>
            <view style="color: #aaaab3">-</view>
            <uni-data-select
              class="shop-category"
              :localdata="thirdColumns"
              v-model="thirdColumnsId"
              @change="chooseThirdColumnsId"
              :clear="false"
              emptyTips="暂无数据"
            ></uni-data-select>
          </view>
          <!-- 一级分类-按钮 end -->
        </view>
        <!-- 品分类 end  -->
        <!-- 商品价格 start -->
        <view>
          <view class="mt-40 ml-25" style="color: #807e7e">商品价格:</view>
          <view class="f d-cf mt-25">
            <u-input
              border="surround"
              fontSize="24rpx"
              v-model="formData.min_price"
              type="number"
              placeholder="￥最低价格"
            ></u-input>
            <view style="color: #aaaab3">-</view>
            <u-input
              border="surround"
              fontSize="24rpx"
              v-model="formData.max_price"
              type="number"
              placeholder="￥最高价格"
            ></u-input>
          </view>
        </view>
        <!-- 商品价格 end -->
        <!-- 商品折扣 start -->
        <view>
          <view class="mt-40 ml-25" style="color: #807e7e">商品折扣:</view>
          <view class="f d-cf mt-25">
            <u-input
              border="surround"
              fontSize="24rpx"
              v-model="formData.profit_form"
              type="number"
              placeholder="最低折扣%"
            ></u-input>
            <view style="color: #aaaab3">-</view>
            <u-input
              border="surround"
              fontSize="24rpx"
              v-model="formData.profit_to"
              type="number"
              placeholder="最高折扣%"
            ></u-input>
          </view>
        </view>
        <!-- 商品折扣 end -->
        <!-- 按钮 -->
        <!-- #ifdef H5 -->
        <view class="f d-ef mt-80 mb-40" style="margin-left: 345rpx">
          <u-button @click="reset" size="large">重置</u-button>
          <u-button
            class="ml-25 mr-45"
            type="error"
            @click="btnOk"
            size="large"
          >
            确定
          </u-button>
        </view>
        <!--  #endif -->
        <!--  #ifdef MP-WEIXIN -->
        <view class="f d-ef mt-80 mb-40 mr-45" style="margin-left: 345rpx">
          <u-button class="mr-25" size="large" @click="reset">重置</u-button>
          <u-button type="error" size="large" @click="btnOk">确定</u-button>
        </view>
        <!--  #endif -->
      </view>
    </view>
  </view>
</template>
<script>
export default {
    data() {
    return {
      page: 1,
      pageSize: 15,
      total: 0,
      isLast: false,
      isActiveSales: 0, // 样式开关：营销活动-按钮
      isActiveSub: 0, // 样式开关：drawer效果-按钮
      isAll: 0, // 一级分类开关：0-全部，1-营销活动，2-其他一级分类
      firstIndex: 0, // 一级分类索引
      parent_id: 0, // 分类入参
      level: 1, // 分类入参
      tapShow: false, // 展开一级drawer
      secondPickerShow: false,
      thirdPickerShow: false,
      pricePopupShow: false,
      editPopupShow: false,
      secondName: '请选择',
      thirdName: '请选择',
      salesBtn: ['全部', '热卖', '促销', '新品'],
      sortForm: {
        value: 'sales', // 价格price, 利润profit, 利润率profit_rate, 销量sales
        sort: '2', // 1为升序，2为降序
      },
      formData: {
        category1_id: null, // 一级分类
        category2_id: null, // 二级分类
        category3_id: null, // 三级分类
        sort_by: null, // 排序：1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
        title: '', // 产品标题
        is_new: null, // 新品
        is_hot: null, // 热卖
        is_promotion: null, // 促销
        min_price: null, // 最低售价（分）
        max_price: null, // 最高售价（分）
        profit_form: null, // 最低折扣
        profit_to: null, // 最高折扣
        is_choose: null, // 只看未选
      },
      // 一级分类数据源
      firstTab: [
        // { name: '营销活动' },
        // { name: '分类一' }
      ],
      // 二级分类数据源
      secondColumns: [
        [
          // { id: 1, name: '中国', parent_id: 111 },
          // { id: 2, name: '美国', parent_id: 112 },
          // { id: 3, name: '日本', parent_id: 113 },
        ],
      ],
      secondColumnsId: null, // 二级分类ID
      // 三级分类数据源
      thirdColumns: [
        [
          // { id: 1, name: '中国2', parent_id: 114 },
          // { id: 2, name: '美国3', parent_id: 115 },
          // { id: 3, name: '日本4', parent_id: 116 },
        ],
      ],
      thirdColumnsId: null, // 三级分类ID
      // 商品列表数据源
      productList: [],
      // 单条商品数据源
      product: {},
      price: '', // 单规格销售价
      headTitle: '商品管理', // Popup标题
      checkboxValue: [],
      isDelete: false, // 是否批量删除
      allCheck: false, // 是否全选
      chooseAllcheck: true, // 是否显示选中
      filterPropShow: false, // 是否显示筛选条件
      isSalesShow: false, // 是否显示营销
      categoryTags: { id: 'all' }, // 分类名称标签
    }
  },
  watch: {
    // 监听商品列表数据源
    productList(newVal, oldVal) {
      if(this.allCheck && newVal.length > 0){
        this.onCheckSome()
      }
    },
  },
  onLoad() {
    this.getInit()
  },
  onReachBottom() {
    if (!this.isLast) {
      this.page = this.page + 1
      this.getProductServe(true)
    }
  },
  methods: {
    search() {
      this.page = 1
      this.getProductServe()
    },
    // 按排序条件查询时的入参
    onChangeSort(val) {
      // console.log('onFilerProduct',val, this.sortForm)
      /**
       * 排序查询：
       * formatObj键 => 1:向上up 升序1; 2:向下down 降序2。
       * formatObj值 => 1综合降 2价格降 3价格升 4销量降5销量升6创建时间降7创建时间升8利润降9利润升10利润率降序11升序
       */
      /* const formatObj = {
        price: {
          1: 3,
          2: 2,
        },
        profit: {
          1: 9,
          2: 8,
        },
        profit_rate: {
          1: 11,
          2: 10,
        },
        sales: {
          1: 5,
          2: 4,
        },
      } */
      this.sortForm = val
      this.search()
    },
    getSortStatus(sortForm) {
      /**
       * 排序查询：
       * formatObj键 => 1:向上up 升序1; 2:向下down 降序2。
       * formatObj值 => 1综合降 2价格降 3价格升 4销量降5销量升6创建时间降7创建时间升8利润降9利润升10利润率降序11升序
       */
      const formatObj = {
        price: {
          1: 3,
          2: 2,
        },
        profit: {
          1: 9,
          2: 8,
        },
        profit_rate: {
          1: 11,
          2: 10,
        },
        sales: {
          1: 5,
          2: 4,
        },
      }
      return formatObj[sortForm.value][sortForm.sort]
    },
    // 按分类条件查询时的入参
    categoryId(cate, level) {
      switch (level) {
        case 1:
          this.formData.category2_id = null
          this.formData.category3_id = null
          break
        case 2:
          this.formData.category3_id = null
          break
        default:
          break
      }
      if (typeof this.parent_id === 'number') {
        this.formData[cate] = this.parent_id
      }
      this.search()
    },
    // 初始化
    getInit() {
      this.getCategoryServe()
      this.getProductServe()
    },
    // 查询一二三级分类
    getCategoryServe() {
      const params = {
        level: this.level,
      }
      if (this.parent_id && typeof this.parent_id === 'number') {
        params.parent_id = this.parent_id
      }
      const api = '/api/smallShop/category/getCategoryByParentID'
      this.get(api, params, true)
        .then(res => {
          if (res.code === 0) {
            if (params.level === 1) {
              this.firstTab = res.data.categories
              const all = {
                id: 'all',
                name: '全部',
              }
              const sales = {
                id: 'sales',
                name: '营销活动',
              }
              this.firstTab.unshift(all, sales)
            }
            if (params.level === 2) {
              // #ifdef H5
              this.secondColumns = []
              this.secondColumns.push({
                ...res.data.categories,
                text: res.data.categories[0].name,
                value: res.data.categories[0].id,
              })
              // #endif
              // #ifdef MP-WEIXIN
              // this.secondColumns[0] = res.data.categories
              this.secondColumns = []
              this.secondColumns.push({
                ...res.data.categories,
                text: res.data.categories[0].name,
                value: res.data.categories[0].id,
              })
              // #endif
            }
            if (params.level === 3) {
              // #ifdef H5
              this.thirdColumns = []
              this.thirdColumns.push({
                ...res.data.categories,
                text: res.data.categories[0].name,
                value: res.data.categories[0].id,
              })
              // #endif
              // #ifdef MP-WEIXIN
              // this.thirdColumns[0] = res.data.categories
              this.thirdColumns = []
              this.thirdColumns.push({
                ...res.data.categories,
                text: res.data.categories[0].name,
                value: res.data.categories[0].id,
              })
              // #endif
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 查询商品列表
    getProductServe(add = false) {
      this.formData.sort_by = this.getSortStatus(this.sortForm)
      // 查询商品列表入参判空
      const temps = { ...this.formData }
      for (const item in temps) {
        if (temps[item] === null) {
          delete temps[item]
        }
      }
      const params = {
        ...temps,
        page: this.page,
        pageSize: this.pageSize,
      }
      if (params.min_price) {
        params.min_price = this.toFen(params.min_price)
      }
      if (params.max_price) {
        params.max_price = this.toFen(params.max_price)
      }
      const api = '/api/smallShop/product/getMyProductList'
      this.get(api, params, true)
        .then(res => {
          if (res.code === 0) {
            if (add) {
              this.productList = [...this.productList, ...res.data.list]
            } else {
              this.productList = res.data.list || []
            }
            this.total = res.data.total
            this.isLast = this.productList.length === this.total ? true : false
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 切换一级分类
    onChangeFirstTab(obj) {
      this.firstIndex = obj.index // 一级标签切换索引
      this.parent_id = this.firstTab[this.firstIndex].id
      this.level = 2
      this.getCategoryServe()
      this.onShowFilter(obj.id)
      console.log('obj.id', obj.id)
      if (obj.id === 'all') {
        this.formData = {
          page: 1,
          pageSize: 15,
        }
      }
      this.categoryId('category1_id', 1)
    },
    // 切换一级分类时，显示其对应的筛选条件
    onShowFilter(id) {
      this.isAll = 0 // 0-全部，1-营销活动，2-其他一级分类
      if (id && typeof id === 'number') {
        this.isAll = 2
      }
      if (id === 'sales') {
        this.isAll = 1
      }
      this.secondName = '请选择'
      this.thirdName = '请选择'
    },
    // 二三级分类picker的数据变化
    onChangePicker(e) {
      const { columnIndex } = e
      // 当第一列值发生变化时，变化第二列(后一列)对应的选项
      if (columnIndex === 0) {
        // picker为选择器this实例，变化第二列对应的选项
        // picker.setColumnValues(1, this.columnData[index])
      }
    },
    // 打开二级分类picker
    onOpenSecondPicker() {
      this.secondPickerShow = true
    },
    // 打开三级分类picker
    onOpenThirdPicker() {
      this.thirdPickerShow = true
    },
    // 确认二级分类picker
    onConfirmSecondPicker(e) {
      console.log('confirm 2nd', e)
      this.secondName = e.value[0].name
      this.parent_id = e.value[0].id
      this.level = 3
      this.getCategoryServe()
      this.categoryId('category2_id', 2)
      this.secondPickerShow = false
    },
    // 确认三级分类picker
    onConfirmThirdPicker(e) {
      console.log('confirm 3rd', e)
      this.thirdName = e.value[0].name
      this.parent_id = e.value[0].id
      this.categoryId('category3_id', 3)
      this.thirdPickerShow = false
    },
    // 一节分类所有按钮
    onClickSubBtn(index, item) {
      this.isActiveSub = index // 更改button样式
      this.thirdColumnsId = null
      this.isSalesShow = false
      this.categoryTags = item
      this.onChangeFirstTab({ index, ...item })
    },
    // 选中二级分类ID
    chooseSecondColumnsId(e) {
      this.level = 3
      this.parent_id = e
      this.secondColumnsId = e
      this.formData.category2_id = e
      this.getCategoryServe()
    },
    // 选中三级分类ID
    chooseThirdColumnsId(e) {
      this.thirdColumnsId = e
      this.formData.category3_id = e
    },
    // 营销活动
    onClickSalesBtn(index, item) {
      this.isActiveSales = index // 更改button样式
      switch (item) {
        case '热卖':
          this.formData.is_hot = 1
          this.formData.is_promotion = null
          this.formData.is_new = null
          break
        case '促销':
          this.formData.is_hot = null
          this.formData.is_promotion = 1
          this.formData.is_new = null
          break
        case '新品':
          this.formData.is_hot = null
          this.formData.is_promotion = null
          this.formData.is_new = 1
          break
        default:
          this.formData.is_hot = null
          this.formData.is_promotion = null
          this.formData.is_new = null
          break
      }
      this.search()
    },
    // 只看未选
    onChangeChoose(val) {
      switch (val) {
        case 1:
          this.formData.is_choose = 1 // is_choose = 1 已选中,  is_choose = 2 未选中   不传值或is_choose=0为全部
          break
        case 2:
          this.formData.is_choose = 2
          break
        default:
          this.formData.is_choose = 0
          break
      }
      this.getProductServe()
    },
    // 提交选品
    onAddProduct(params) {
      const api = '/api/smallShop/product/changePrice'
      let newParams = {
        product_id: params.product_ids[0],
        price_proportion: params.price_proportion,
        price_type: params.price_type,
      }
      this.post(api, newParams, true)
        .then(res => {
          if (res.code === 0) {
            this.getProductServe()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 批量改价
    onChangePrice(params) {
      const api = '/api/smallShop/product/batchChangePrice'
      this.post(api, params, true)
        .then(res => {
          if (res.code === 0) {
            this.getProductServe()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 打开商品管理popup
    onOpenPricePopup(item) {
      this.pricePopupShow = true
      this.product = item
      this.$refs.pricePopup.price_proportion =
        item.small_shop_product_sale.price_proportion / 100
      this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
      this.$nextTick(() => {
        this.$refs.pricePopup.computedSellingPrice()
      })
    },
    // 打开批量改价popup
    onOpenEditPopup() {
      this.editPopupShow = true
    },
    // 确认商品管理popup
    onConfirmPricePopup(p) {
      this.onAddProduct(p)
      this.onClosePricePopup()
    },
    // 确认批量改价popup
    onConfirmEditPopup(p) {
      this.onChangePrice(p)
      this.onCloseEditPopup()
    },
    // 关闭商品管理popup
    onClosePricePopup(flg = '') {
      if (!flg) {
        this.pricePopupShow = false
        this.editPopupShow = false
      } else {
        this[flg] = false
      }
    },
    // 关闭批量改价popup
    onCloseEditPopup() {
      this.editPopupShow = false
    },
    // 筛选
    filterType() {
      this.filterPropShow = true
    },
    onCheckSome(n) {
      if (n) {
        console.log(n, 1111)
        this.checkboxValue = []
        this.productList.map(element => {
          if (n.includes(element.id)) {
            this.checkboxValue.push(element.id)
          }
          return this.checkboxValue
        })
        if (this.productList.length === this.checkboxValue.length) {
          this.$nextTick(() => {
            this.chooseAllcheck = false
          })
        } else {
          this.$nextTick(() => {
            this.chooseAllcheck = true
          })
        }
      } else {
        this.checkboxValue = []
        if (this.allCheck) {
          this.productList.map(ele => {
            this.checkboxValue.push(ele.id)
          })
        }
      }
    },
    // 选择批量删除
    checkoutDel() {
      this.isDelete = true
    },
    // 删除商品(单条、批量)
    onDelete(id, index = null) {
      const params = {
        product_ids: [id],
      }
      if (this.checkboxValue === undefined) {
        params.product_ids[0] = id // 非单选框单挑删除
      } else {
        if (this.checkboxValue.length === 0) params.product_ids[0] = id // 单选框单挑删除
        if (this.checkboxValue.length !== 0)
          params.product_ids = this.checkboxValue // 复选框多条删除
      }
      const api = '/api/smallShop/product/deleteProduct'
      this.delete(api, params, true)
        .then(res => {
          if (res.code === 0) {
            // this.page = 1
            this.$nextTick(() => {
              this.checkboxValue = []
            })
            if (index !== null) {
              this.productList.splice(index, 1)
            } else {
              this.getProductServe()
            }
            // this.getProductServe()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 取消删除
    cancelDel() {
      this.isDelete = false
      if (this.allCheck) {
        this.allCheck = !this.allCheck
      }
      this.checkboxValue = []
    },
    // 全选删除
    allCheckbox() {
      this.allCheck = !this.allCheck
      // this.chooseAllcheck = false
      this.onCheckSome()
    },
    // 商品详情跳转
    goProductDetail(id) {
      this.navTo(
        '/packageA/commodity/commodity_details/commodity_details?id=' +
          id +
          '&isProductManage=1',
      )
    },
    // 筛选重置
    reset() {
      this.isActiveSub = 0
      this.isAll = 0
      this.secondColumnsId = null
      this.thirdColumnsId = null
      this.formData.min_price = null
      this.formData.max_price = null
      this.formData.profit_form = null
      this.formData.profit_to = null
      this.formData.category1_id = null
      this.formData.category2_id = null
      this.formData.category3_id = null
      this.filterPropShow = false
      this.categoryTags.id = 'all'
      this.getProductServe()
    },
    // 确认筛选
    btnOk() {
      this.isSalesShow = true
      this.filterPropShow = false
      this.getProductServe()
    },
    // 关闭分类名称标签
    closeCategoryTags() {
      this.isActiveSub = 0
      this.isAll = 0
      this.formData.category1_id = null
      this.formData.category2_id = null
      this.formData.category3_id = null
      this.secondColumnsId = null
      this.formData.is_hot = null
      this.formData.is_new = null
      this.formData.is_promotion = null
      this.categoryTags.id = 'all'
      this.getProductServe()
    },
    // 关闭二级分类名称标签
    closeCategory2Tags() {
      this.secondColumnsId = null
      this.thirdColumnsId = null
      this.formData.category2_id = null
      this.formData.category3_id = null
      this.formData.is_hot = null
      this.formData.is_new = null
      this.formData.is_promotion = null
      this.getProductServe()
    },
    // 关闭三级级分类名称标签
    closeCategory3Tags() {
      this.thirdColumnsId = null
      this.formData.category3_id = null
      this.formData.is_hot = null
      this.formData.is_new = null
      this.formData.is_promotion = null
      this.getProductServe()
    },
    // 关闭价格标签
    closePriceTags() {
      this.formData.min_price = null
      this.formData.max_price = null
      this.getProductServe()
    },
    // 关闭折扣标签
    closeProfitTags() {
      this.formData.profit_form = null
      this.formData.profit_to = null
      this.getProductServe()
    },
  },
}
</script>
<style scoped>
::v-deep .u-tabs__wrapper__nav__item__text.data-v-0de61367 {
  font-size: 28rpx;
}
::v-deep .u-button {
  font-size: 28rpx;
}
::v-deep .font_size12 {
  font-size: 28rpx;
}
::v-deep .item-count {
  display: flex;
  flex-direction: column;
}
::v-deep .u-tabs__wrapper__nav__item__text.data-v-0de61367 {
  white-space: nowrap;
  /* text-overflow: ellipsis; */
}
::v-deep .u-picker__view__column__item.data-v-d45639b2 {
  height: 34px !important;
  line-height: 34px !important;
}
::v-deep .u-button--info.data-v-3bf2dba7 {
  font-size: 28rpx;
}
::v-deep .u-input {
  margin: 0 20rpx;
  height: 40rpx;
  padding: 6rpx 18rpx !important;
  background: white;
}
::v-deep .u-button--mini {
  height: 48rpx !important;
}
::v-deep .u-button--small {
  height: 56rpx !important;
}
::v-deep .u-button__text {
  font-size: 26rpx !important;
}
::v-deep .u-icon__icon.data-v-172979f2 {
  padding-left: 10rpx;
}
::v-deep .uni-stat__select[data-v-6b64008e] {
  padding-bottom: 0;
}
::v-deep .uni-select__input-text[data-v-6b64008e] {
  width: 254rpx;
}
::v-deep .shop-category .uni-select__input-text {
  width: 254rpx;
}
::v-deep .u-input {
  margin: 0 44rpx 0 24rpx;
  height: 56rpx;
}
::v-deep .u-tag-wrapper.data-v-1481d46d {
  margin-bottom: 30rpx;
}
::v-deep .u-button.data-v-3bf2dba7 {
  height: 56rpx;
  line-height: 56rpx;
  margin-right: 20rpx;
}
::v-deep .u-icon__icon .uicon-checkbox-mark {
  font-size: 26rpx;
}
</style>
<style lang="scss" scoped>
.head {
  padding: 40rpx 30rpx 0 30rpx;
  background-color: #fff;
}
.head-shadow {
  box-shadow: 0 5px 6px -6px #999;
}
.con-tabs {
  padding-bottom: 12rpx;
  // background: pink;
  .con-drawer {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;
    // background-color: orange;
    .activeSub {
      color: #ea4e4c !important;
      background-color: #f8d8d4;
      border: 1px solid #ea4e4c;
      transition: color 0.2s linear, background-color 0.2s linear,
        border-color 0.2s linear;
    }
  }
}
.con-btn {
  display: flex;
  justify-content: flex-end;
}
.btn-txt {
  height: 68rpx;
}
.con-sift {
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  padding: 0 40rpx;
}
.con-list {
  padding: 30rpx 20rpx;
}
.btn-1 {
  width: 100rpx;
  height: 54rpx;
  line-height: 54rpx;
  text-align: center;
  border-radius: 10px;
}
.activeSales {
  color: #ffffff;
  width: 110rpx;
  height: 50rpx;
  background: #f51d1d;
  border-radius: 25rpx 25rpx 25rpx 25rpx;
}

.con-sort {
  margin-top: 30rpx;
  width: 60vw;
}
.con-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 30rpx;
  width: 30vw;
}
.con-range {
  display: flex;
  padding-top: 30rpx;
  width: 100vw;
  .item-range {
    display: flex;
    align-items: center;
    width: 45vw;
  }
  .item-range:nth-child(1) {
    margin-right: 2vw;
  }
}
.w100 {
  width: 100vw;
}
.w63 {
  width: 63vw;
}
.line2 {
  overflow: hidden;
}
.relative {
  position: relative;
}
.checkbox {
  position: absolute;
  top: 33rpx;
  left: 14rpx;
  z-index: 9;
}
.del-pop {
  width: 100%;
  height: 112rpx;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  z-index: 10;
  .check-size {
    font-size: 28rpx;
    color: #00001c;
  }
}
.filter-pop {
  width: 100%;
  height: 100%;
  background-color: #7b7b7b;
  position: fixed;
  z-index: 99999;
  top: 128rpx;
  .shop-filter {
    width: 100%;
    background-color: #ffffff;
    position: fixed;
    z-index: 100000;
    top: 128rpx;
    .scroll-filter {
      height: 320rpx;
      width: 100%;
      .btn-2 {
        margin: 20rpx 21rpx 0 0;
        padding: 0 36rpx;
        height: 64rpx;
        line-height: 64rpx;
        text-align: center;
        color: #00001c;
        border: 1px solid #e8e8e8;
        background-color: #e8e8e8;
        border-radius: 10px;
      }
      .activeSub {
        color: #f51d1d !important;
        transition: color 0.2s linear, background-color 0.2s linear,
          border-color 0.2s linear;
        background: rgba(245, 29, 29, 0.1);
        border-radius: 10rpx;
        border: 1rpx solid #f51d1d;
      }
    }
  }
}
</style>

