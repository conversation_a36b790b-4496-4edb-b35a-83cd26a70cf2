<template>
  <view class="components-index">
    <view class="header">
      <text class="title">公共组件库</text>
      <text class="subtitle">My Components Library</text>
    </view>
    
    <view class="content">
      <view class="description">
        <text>这是一个公共组件分包的索引页面</text>
        <text>用于支持组件的分包加载机制</text>
      </view>
      
      <view class="component-list">
        <text class="list-title">已注册组件 ({{ componentCount }} 个):</text>
        <view class="components">
          <view class="component-item" v-for="(component, index) in componentList" :key="index">
            <text class="component-name">{{ component }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="footer">
      <text>此页面仅用于分包支持，通常不会被用户访问</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ComponentsIndex',
  data() {
    return {
      componentList: [
        'myAgreement',
        'myBeauty', 
        'myClassifyModule',
        'myClassifyGoods',
        'myMoveAlbum',
        'myMoveGoods',
        'myMoveSuppliers',
        'myOrder',
        'myPrivacyAuthPopup',
        'myPromotionList',
        'myScreeningOfGoods',
        'myShade',
        'mySmall',
        'mySortBy',
        'myTabbar',
        'myToTop',
        'myUnloggedPage',
        'myHeaderBar',
        'myWallet',
        'myAddressPopup',
        'myAddressFromPopup',
        'myEasySelect',
        'myFailureGoods',
        'myInvoicePopup',
        'myLocalLife',
        'myPaymentPopup',
        'myReceivingPopup',
        'myShareAlbums',
        'myStreetPopup',
        'mySuppliers'
      ]
    }
  },
  computed: {
    componentCount() {
      return this.componentList.length
    }
  }
}
</script>

<style lang="scss" scoped>
.components-index {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #7f8c8d;
  }
}

.content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.description {
  margin-bottom: 40rpx;
  
  text {
    display: block;
    font-size: 30rpx;
    color: #34495e;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }
}

.component-list {
  .list-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 30rpx;
  }
}

.components {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.component-item {
  background: #ecf0f1;
  padding: 15rpx 25rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #3498db;
}

.component-name {
  font-size: 24rpx;
  color: #2c3e50;
  font-family: 'Courier New', monospace;
}

.footer {
  text-align: center;
  
  text {
    font-size: 24rpx;
    color: #95a5a6;
    font-style: italic;
  }
}
</style>
