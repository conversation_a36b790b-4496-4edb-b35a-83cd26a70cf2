﻿<template>
	<view>
		<view :style="{'display':(flagShow===false? 'none':'block')}" @click="toTop" class="btn">
			<view class="d-cc topcs">
				<view class="iconfont icon-fontclass-zhiding" style="color: #FFFFFF;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
    name: 'myToTop',
		data() {
			return {
				flagShow:false, //是否显示返回顶部按钮	
			}
		},
		props:['flage'],
		onShow() {
			console.log(this.$store.state.flag)
		},
		watch: {
			flage(newValue, oldValue) {
				this.flagShow = newValue
			}
		},
		methods: {
			// 返回顶部
			toTop() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 100,
				});
			},
		},
	}
</script>

<style>
	.btn {
		/* 返回顶部绝对定位*/
		position: fixed;
		z-index: 9999;
		right: 32rpx;
		bottom: 260rpx;
		display: none;
	}

	.topcs {
		background-color: #999999;
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}
</style>

