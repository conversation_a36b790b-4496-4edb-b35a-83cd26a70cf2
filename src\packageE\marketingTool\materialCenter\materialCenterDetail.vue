﻿<template>
  <view class="detail-box">
    <view class="f fac detail-top">
      <u-image
        :src="detail.user.avatar"
        width="80rpx"
        height="80rpx"
        shape="circle"
      ></u-image>
      <view class="ml_10 f fac">
        <view>
          <view class="blod black fs-2-5">
            {{
              detail.user.nickname
                ? detail.user.nickname
                : '用户' + detail.user.username.slice(-4)
            }}
          </view>
          <view class="mt_10 grey fs-1">
            {{ formatDateTime(detail.created_at) }}
          </view>
        </view>
        <view class="f fac grey mt_40 ml_30 fs-1">
          <u-icon name="eye"></u-icon>
          <view class="ml_5">{{ detail.visit_number }}</view>
        </view>
      </view>
    </view>
    <view class="mt_30 fs-3 black blod">{{ detail.title }}</view>
    <view class="mt_30 black fs-2-5">{{ detail.content }}</view>
    <view class="mt_40">
      <view
        v-for="(items, index) in detail.img_url"
        :key="index"
        class="pb-20"
        @click="downImage(items)"
      >
        <!-- <u--image
          v-if="detail.img_url[0]"
          :src="items"
          width="690rpx"
          radius="39rpx"
        ></u--image> -->
        <img
          v-if="detail.img_url[0]"
          :src="items"
          style="max-width: 690rpx; border-radius: 12rpx"
        />
      </view>
      <video
        v-if="detail.video_url"
        style="width: 690rpx; height: 512rpx"
        :src="detail.video_url"
        controls
      />
    </view>
    <view
      v-if="detail.product.id !== 0"
      class="product-box mt_25 f fac"
      @click="goProduct(detail.product_id)"
    >
      <view class="ml_15">
        <u-image
          :src="detail.product.image_url"
          width="128rpx"
          height="128rpx"
          radius="20rpx"
        ></u-image>
      </view>
      <view class="ml_15">
        <view class="fw-500 black fs-2 ell">
          {{ detail.product.title }}
        </view>
        <view class="c-orange fw-500 fs-2 mt_15">
          ￥ {{ toYuan(detail.product.price) }}
        </view>
      </view>
    </view>
    <view class="icon-box mt_30 f fac">
      <view class="f fac f1 fjc" @click="downloadImgStr(detail)">
        <!-- #ifdef MP-WEIXIN -->
        <view class="iconfont icon-fontclass-xiazai"></view>
        <view class="ml_10">下载图文</view>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS || H5 -->
        <view class="iconfont icon-fuzhi"></view>
        <view class="ml_10">复制图文</view>
        <!-- #endif -->
      </view>
      <view class="f fac f1 fjc" @click="getPoster(detail)">
        <view class="iconfont icon-material_goodsCode"></view>
        <view class="ml_10">商品码</view>
      </view>
      <!-- #ifdef MP-WEIXIN -->
      <view class="f fac f1 fjc">
        <button
          open-type="share"
          class="clear-btn c-white font_size14"
          style="line-height: 45rpx"
        >
          <view class="f fac">
            <view class="iconfont icon-fenxiang1 black"></view>
            <view class="ml_10 black">分享</view>
          </view>
        </button>
      </view>
      <!-- #endif -->
      <!-- #ifdef APP-PLUS || H5 -->
      <view class="f fac f1 fjc">
        <button
          @click="$store.commit('upShadeShow', true)"
          class="clear-btn c-white font_size14"
          style="line-height: 45rpx"
        >
          <view class="f fac">
            <view class="iconfont icon-fenxiang1 black"></view>
            <view class="ml_10 black">分享</view>
          </view>
        </button>
      </view>
      <!-- #endif -->
    </view>
    <!-- 遮罩层 -->
    <my-shade></my-shade>
    <!-- #ifdef MP-WEIXIN -->
    <u-popup :show="imageShow" mode="center" @close="imageShow = false">
      <u--image :src="downImages" width="600rpx" height="960rpx"></u--image>
      <!-- <img
        :src="downImages"
        style="max-width: 600rpx"
        show-menu-by-longpress="1"
      /> -->
    </u-popup>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <u-popup :show="imageShow" mode="center" @close="imageShow = false">
      <!-- <u--image :src="downImages" width="600rpx" height="960rpx"></u--image> -->
      <img
        :src="downImages"
        style="max-width: 600rpx"
        show-menu-by-longpress="1"
      />
    </u-popup>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
    data() {
    return {
      detail: {},
      imageShow: false,
      downImages: '',
    }
  },
  onLoad(options) {
    this.getMaterialCenterDetail(options.id)
  },
  // 分享
  onShareAppMessage() {
    let that = this
    return {
      title: that.detail.title,
      path:
        '/packageE/marketingTool/materialCenter/materialCenterDetail?id=' +
        that.detail.id,
    }
  },
  methods: {
    // 获取素材中心详情
    async getMaterialCenterDetail(id) {
      const data = {
        id: parseInt(id),
      }
      const res = await this.post('/api/material/center/detail', data)
      if (res.code === 0) {
        this.detail = res.data
        res.data.img_url = res.data.img_url.split(',')
      }
    },
    // 下载图片
    downImage(item) {
      this.imageShow = true
      this.downImages = item
    },
    // 下载图文
    downloadImgStr(item) {
      // 下载图片部分
      if (item.img_url.lengh !== 0) {
        item.img_url.forEach(url => {
          uni.downloadFile({
            url: url,
            success: res => {
              if (res.statusCode === 200) {
                uni.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: function (img) {
                    console.log(img, '图片下载成功')
                  },
                })
              }
            },
          })
        })
      }
      // 下载视频部分
      if (item.video_url) {
        uni.downloadFile({
          url: item.video_url,
          success: function (res) {
            if (res.statusCode === 200) {
              uni.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function (video) {
                  console.log(video, '图片下载成功')
                },
              })
            }
          },
        })
      }
      uni.setClipboardData({
        data: item.content,
        success: function () {
          console.log('成功复制文字')
        },
      })
    },
    // 生成海报
    async getPoster(item) {
      let data = {
        id: parseInt(item.product_id),
      }
      /*#ifdef MP-WEIXIN*/
      data.qr = 2
      /*#endif*/
      /*#ifdef H5*/
      data.qr = 1
      /*#endif*/
      const res = await this.post('/api/material/product/poster', data)
      if (res.code === 0) {
        this.imageShow = true
        this.downImages = res.data.link
      }
    },
    // 跳转商品详情
    goProduct(id) {
      this.navTo(
        '/packageA/commodity/commodity_details/commodity_details?id=' + id,
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-box {
  padding: 18rpx 30rpx 100rpx 33rpx;
  background-color: #ffffff;
}
.product-box {
  width: 692rpx;
  height: 160rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
}
.icon-box {
  width: 702rpx;
  height: 110rpx;
}
.clear-btn {
  border-radius: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  padding: unset;
  margin: unset;

  &::after {
    border: unset;
  }
}
.black {
  color: #161616;
}
.blod {
  font-weight: bold;
}
.grey {
  color: #808080;
}
.fw-500 {
  font-weight: 500;
}
</style>

