﻿<template>
  <!-- 首页供应商 -->
  <view class="p-20">
    <!-- <view>显示供应商的数据列表</view> -->
    <view v-for="item in list" :key="item" class="sup-view bg-white p-20">
      <view class="f" @click="jumpA(item)">
        <u--image
          :showLoading="true"
          width="120rpx"
          height="120rpx"
          :src="item.shop_logo ? item.shop_logo : logo_img"
        ></u--image>
        <view class="ml_20 f1">
          <view class="f fjsb">
            <view class="f1 ell" style="height: 20px; font-weight: 700">
              {{ item.shop_name }}
            </view>
            <view v-if="classifyCheck === 'supply'">供应链</view>
            <view >{{ item.category_info.name }}</view>
          </view>
          <view class="f fac statistics-view mt_15">
            <view>
              商品数量:
              <text class="c-f1 ml_20">{{ item.goods_count }}</text>
            </view>
            <view class="ml_40">
              热销 (件):
              <text class="c-f1 ml_20">{{ item.hot_sale }}</text>
            </view>
          </view>
        </view>
      </view>

      <scroll-view
        class="scroll-view_H goods-box"
        scroll-x="true"
        scroll-left="0"
      >
        <view class="f">
          <view
            v-for="product in cutOut(item.product)"
            :key="product.id"
            class="goods-item"
            @click="
              navTo(
                '/packageA/commodity/commodity_details/commodity_details?id=' +
                  product.id,
              )
            "
          >
            <u--image
              :showLoading="true"
              width="200rpx"
              height="200rpx"
              :src="product.thumb"
            ></u--image>
            <view class="pl mt_20 mb_10">{{ product.title }}</view>
            <view class="c-orange" v-if="checkNull(user)">
              ¥{{ toYuan(product.price) }}
            </view>
            <view class="c-orange" v-else>价格登录可见</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
      <view v-if="list.length !== 0">暂无更多~</view>
      <view v-if="list.length == 0">暂无数据~</view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'mySuppliers',
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      user: uni.getStorageSync('user'),
      logo_img: uni.getStorageSync('h5_logo'),
      classifyCheck: 'all'
    }
  },
  methods: {
    jumpA(item) {
      let _type = this.classifyCheck === 'supply' ? 1 : 0
      this.navTo(
        '/packageB/store/storeDetails?id=' + item.id + '&type=' + _type,
      )
    },
    cutOut(arr) {
      let newArr = arr
      if (arr && arr.length > 4) {
        newArr = arr.splice(4)
      }
      return newArr
    },
  },
}
</script>
<style lang="scss" scoped>
.sup-view {
  border-radius: 10rpx;
  margin-top: 20rpx;
  .statistics-view {
    font-size: 22rpx;
  }
}
.goods-box {
  margin-top: 20rpx;
  .goods-item {
    min-width: 200rpx;
    height: 320rpx;
    margin-right: 20rpx;
    &:last-child {
      margin-right: 0;
    }
  }
}
.scroll-view_Y {
  height: calc(100vh - 75px);
}
</style>

