﻿<!-- 横向以及竖向商品展示组件整合 -->
<template>
	<view>
		<!-- 商品展示筛选模块 -->
		<view>
			<view>
				<view class="d-bf m-20 ml-40 mr-30" v-if="!enterAlbum">
					<view class="d-cc">
						<view v-for="(item,index) in list3" :key="index" :class="index == 0? '':'ml-80'" class="d-cc"
							@click="screen(item)">
							<view class="fs-1-5">
								{{item.name}}
							</view>
							<!-- #ifdef APP-PLUS || H5 -->
							<view style="transform: scale(0.45);">
								<u-icon name="arrow-up-fill" :color="item.sort == 'top'?'#f14d4d':'#959595'" />
								<u-icon name="arrow-down-fill" :color="item.sort == 'button'?'#f14d4d':'#959595'" />
							</view>
							<!-- #endif -->
							<!-- #ifdef MP-WEIXIN-->
							<view class="d-cc-c ml-5 mt_2">
								<u-icon name="arrow-up-fill" :color="item.sort == 'top'?'#f14d4d':'#959595'" size="13">
								</u-icon>
								<u-icon name="arrow-down-fill" :color="item.sort == 'button'?'#f14d4d':'#959595'"
									size="13">
								</u-icon>
							</view>
							<!-- #endif -->
						</view>
					</view>
					<view class="d-cc">
						<view :class="imgchck" class="iconfont" @click="sortSwitch()" style="font-size: 25rpx;"></view>
						<view class="d-f ml_27" v-if="type == 'screen'">
							<view style="width: 2rpx;height: 23rpx;background-color: #d4d4d4;"></view>
							<view class="iconfont icon-fontclass-shaixuan1 ml_27 fs-0-5" style="font-size: 22rpx;"
								@click="show=true"></view>
						</view>
					</view>
				</view>
				<!-- 横向展示样式一 -->
				<view class="bg-white p-20 mb-20 ml-20 swhb radius10 mr-20" v-show="chck == 'crosswise'"
					v-for="(item,index) in list" :key="index" @click="goCommodity_details(item.id)"
					:class="enterAlbum == true?'mt_20':''">
					<view class="d-f">
						<view>
							<u--image :showLoading="true" :src="item.thumb ? item.thumb : item.image_url" width="150rpx" height="150rpx" radius="10">
							</u--image>
						</view>
						<view class="ml-20">
							<view style="width: 501rpx;" class="fs-2 ell ">
								{{item.title}}
							</view>
							<view v-if="item.is_display!=0">
								<view class="d-bf mt_17 fs-1-5" style="width: 510rpx;">
									<view class="c-7e7e7e fs-1"><text>库存:</text><text class="ml-5">{{item.stock}}</text>
									</view>
								</view>
								<view class="fw-b c-f14e4e mt-10 fs-1">
									<text>利润:</text>
									<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)">{{'¥'+toYuan(item.level_profit ? item.level_profit : item.profit)}}</text>
									<text v-else-if="userLevelPriceTitle">{{'¥'+userLevelPriceTitle}}</text>
									<text v-else @click.stop="jumpLogin">{{checkNull(user)?'¥'+toYuan(item.level_profit ? item.level_profit : item.profit):'价格登录可见'}}</text>
								</view>
							</view>
							<view v-else style="background-color: #F0F0F0;width:100rpx;"
								class=" c-888 fs-0 d-cc radius30 pt_5 pb_5 mt-20">已失效</view>
						</view>
					</view>
					<view class="d-bf mt-25 pr-20 fs-1" v-if="item.is_display!=0">
						<view class="d-cc-c" v-if="page_setup_setting.pc_list_setting.is_web_super_wholesal_price === 0">
							<view v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="c-f14e4e">{{'¥'+toYuan(enterAlbum ? item.agreement_price : item.min_price)}}</view>
							<view v-else-if="userLevelPriceTitle" class="c-f14e4e">{{'¥'+userLevelPriceTitle}}</view>
							<view v-else class="c-f14e4e" @click.stop="jumpLogin">{{checkNull(user)?'¥'+toYuan(enterAlbum ? item.agreement_price : item.min_price):'价格登录可见'}}</view>
							<view class="mt-15">{{page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' : page_setup_setting.language_setting.super_wholesal_price}}</view>
						</view>
						<view class="d-cc-c" v-if="page_setup_setting.pc_list_setting.is_web_price === 0">
							<view v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="c-f14e4e">{{'¥'+toYuan(item.min_normal_price ? item.min_normal_price : item.normal_price)}}</view>
							<view v-else-if="userLevelPriceTitle" class="c-f14e4e">{{'¥'+userLevelPriceTitle}}</view>
							<view v-else class="c-f14e4e" @click.stop="jumpLogin">{{checkNull(user)?'¥'+toYuan(item.min_normal_price ? item.min_normal_price : item.normal_price):'价格登录可见'}}</view>
							<view class="mt-15">{{page_setup_setting.language_setting.price === '' ? '批发价' : page_setup_setting.language_setting.price}}</view>
						</view>
						<view class="d-cc-c" v-if="page_setup_setting.pc_list_setting.is_web_suggested_retail_price === 0">
							<view v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="c-f14e4e">{{'¥'+toYuan(item.origin_price ? item.origin_price : item.guide_price)}}</view>
							<view v-else-if="userLevelPriceTitle" class="c-f14e4e">{{'¥'+userLevelPriceTitle}}</view>
							<view v-else class="c-f14e4e">¥{{toYuan(item.origin_price ? item.origin_price : item.guide_price)}}</view>
							<view class="mt-15">{{page_setup_setting.language_setting.suggested_retail_price === '' ? '建议零售价' : page_setup_setting.language_setting.suggested_retail_price}}</view>
						</view>
						<view class="d-cc-c">
							<view class="c-f14e4e">{{item.sales}}</view>
							<view class="mt-15">已售</view>
						</view>
						<view class="iconfont icon-fontclass-gouwuche" style="color: #ff6c00;font-size: 30rpx;">
						</view>
					</view>
				</view>
				<!-- 竖向展示 -->
				<view v-show="chck == 'vertical'">
					<view>
						<!-- 竖向展示-->
						<view class="d-bf2 m-20" style="flex-flow: wrap;">
							<view class="bg-white swhb2 radius10 " v-for="(item,index) in list" :key="index"
								@click="goCommodity_details(item.id)">
								<view>
									<u--image :showLoading="true" width="350rpx" height="350rpx" :src="item.thumb">
									</u--image>
								</view>
								<view class="p-20 fs-0">
									<view class="fw-b ell fs-1-5">{{item.title}}</view>
									<view v-if="item.is_display!=0">
										<view class="c-7e7e7e fs-2 mt_17">
											<text>已售</text><text class="ml-15">{{item.sales}}</text>
										</view>
										<view v-if="page_setup_setting.pc_list_setting.is_web_super_wholesal_price === 0" class="mt-20 fs-1">
											<text style="color:#3e3e3e;">{{page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' : page_setup_setting.language_setting.super_wholesal_price}}</text>
											<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)"
												class="c-f14e4e ml-15">{{'¥'+toYuan( enterAlbum ? item.agreement_price : item.min_price)}}</text>
											<text v-else-if="userLevelPriceTitle"
												class="c-f14e4e ml-15">{{'¥'+userLevelPriceTitle}}</text>
											<text v-else
												class="c-f14e4e ml-15" @click.stop="jumpLogin">{{checkNull(user)?'¥'+toYuan( enterAlbum ? item.agreement_price : item.min_price):'价格登录可见'}}</text>
										</view>
										<view v-if="page_setup_setting.pc_list_setting.is_web_price === 0" class="mt-15 fs-1">
											<text>{{page_setup_setting.language_setting.price === '' ? '批发价' : page_setup_setting.language_setting.price}}</text>
											<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)"
												class="ml-15 c-f14d4d">{{'¥'+toYuan(item.min_normal_price)}}</text>
											<text v-else-if="userLevelPriceTitle"
												class="ml-15 c-f14d4d">{{'¥'+userLevelPriceTitle}}</text>
											<text v-else
												class="ml-15 c-f14d4d" @click.stop="jumpLogin">{{checkNull(user)?'¥'+toYuan(item.min_normal_price):'价格登录可见'}}</text>
										</view>
										<view v-if="page_setup_setting.pc_list_setting.is_web_suggested_retail_price === 0" class="mt-15 fs-1">
											<text>{{page_setup_setting.language_setting.suggested_retail_price === '' ? '建议零售价' : page_setup_setting.language_setting.suggested_retail_price}}</text>
											<text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)"
												class="ml-15 c-f14d4d">{{'¥'+toYuan(item.origin_price)}}</text>
											<text v-else-if="userLevelPriceTitle"
												class="ml-15 c-f14d4d">{{'¥'+userLevelPriceTitle}}</text>
											<text v-else class="ml-15 c-f14d4d">¥{{toYuan(item.origin_price)}}</text>
										</view>
										<view v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="pt_8 pb_7 pl_19 pr_18 mt-20 make fs-0-5" @click.stop="jumpLogin">
											单笔赚 {{'¥'+toYuan(item.level_profit)}}
										</view>
										<view v-else-if="userLevelPriceTitle" class="pt_8 pb_7 pl_19 pr_18 mt-20 make fs-0-5" @click.stop="jumpLogin">
											单笔赚 {{'¥'+userLevelPriceTitle}}
										</view>
										<view v-else class="pt_8 pb_7 pl_19 pr_18 mt-20 make fs-0-5" @click.stop="jumpLogin">
											单笔赚 {{checkNull(user)?'¥'+toYuan(item.level_profit):'价格登录可见'}}
										</view>
									</view>
									<view v-else style="background-color: #F0F0F0;width:100rpx;"
										class="c-888 fs-0 d-cc radius30 pt_5 pb_5 mt-15">已失效</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="d-cc fs-1 c-5e5e5e mb-25 mt-25" :style="chck == 'vertical'?'margin-top:-13rpx':''">
					<view v-if="readMore&&list.length != 0">暂无更多~</view>
					<view v-if="list.length == 0">暂无数据~</view>
				</view>
			</view>
		</view>
		<!--  -->
		<!-- 价格筛选 -->
		<view>
			<u-action-sheet :show="show" @close="show = false" round="25" title="筛选">
				<view>
					<!-- 筛选输入框 -->
					<view class="mt-10">
						<view class="d-cf fs-1-5 mb_66" v-for="(item,index) in list4" :key="index">
							<view class="ml-30">
								<view class="d-cf ml-10" style="color: #202020;">{{item.title}}</view>
								<view class="mt_39 d-cf">
									<view style="width: 171rpx;height: 60rpx;margin-left: 20rpx;">
										<u--input :placeholder="item.name" type="number" border="surround"
											shape='circle' inputAlign='center' color="#9e9e9e" v-model="item.value">
										</u--input>
									</view>
									<view style="width: 50rpx;height: 2rpx;background-color: #d5d5d5;" class="ml-20">
									</view>
									<view style="width: 171rpx;height: 60rpx;margin-left: 20rpx;">
										<u--input :placeholder="item.name1" border="surround" type="number"
											shape='circle' inputAlign='center' color="#9e9e9e" v-model="item.value1">
										</u--input>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="d-cc mb_30 mt_100">
						<view class="d-cc resetBtnCss" @tap="clearValue()">重置</view>
						<view class="d-cc confirmBtnCss" @click="filtrate">确定</view>
					</view>
				</view>
			</u-action-sheet>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			type: { //type screen为显示筛选
				type: String,
				default: 'default'
			},
			enterAlbum: { //是否为专辑进入
				type: Boolean,
				default: false
			},
			list: { //商品信息
				type: Array
			},
			user: {
				default: uni.getStorageSync('user')
			}
		},
		data() {
			return {
				page_setup_setting:{
					pc_list_setting:{},
					details_setting:{},
					language_setting:{}
				},
				chck: 'crosswise', //商品显示排版
				imgchck: 'icon-fontclass-pailie', //商品排版图标切换
				show: false, //筛选弹框
				list3: [{ //二级分类
						name: 'myScreeningOfGoods',
						sort: ''

					}, {
						name: 'myScreeningOfGoods',
						sort: ''
					},
					{
						name: 'myScreeningOfGoods',
						sort: ''
					}
				],
				list4: [{ //价格赛选数据
						title: '批发价格区间',
						name: 'myScreeningOfGoods',
						value: '',
						name1: '最低价',
						value1: ''
					},
					{
						title: '利润价格区间',
						name: 'myScreeningOfGoods',
						value: '',
						name1: '最低价',
						value1: ''
					}
				]
			}
		},
		computed: {
			userLevelPriceTitle(){
				return this.$store.state.userLevelPriceTitle || ""
			},
			readMore() { //为了适配小程序端{{}}不能直接读取vuex值的问题
				return this.$store.state.homeNoMore
			}
		},
		created() {
			this.list4[0].value = this.$store.state.searchGoods.price_to
			this.list4[0].value1 = this.$store.state.searchGoods.price_form
			this.list4[1].value = this.$store.state.searchGoods.profit_to
			this.list4[1].value1 = this.$store.state.searchGoods.profit_form
			this.get('/api/home/<USER>', {}, true).then(data => {
				this.page_setup_setting = data.data.page_setup_setting
			})
		},
		methods: {
			jumpLogin(){
				if (this.checkWenxin() && !this.checkNull(this.user)) {
					// 公众号登录
					this.isWeiXinBrowser()
					return;
				}
				if(!this.user){
					this.navTo("/pages/login/login")
				}
			},
			screen(e) {
				let arry = []
				for (var i = 0; i < this.list3.length; i++) {
					arry.push(i)
				}
				/* 筛选选中状态判断*/
				for (var i = 0; i < this.list3.length; i++) {
					if (this.list3[i].name === e.name) {
						if (e.sort == 'top') {
							this.$set(this.list3[i], 'sort', 'button')
							for (var j = 0; j < arry.length; j++) {
								if (arry[j] != i) {
									this.$set(this.list3[j], 'sort', '')
								}
							}
						} else {
							this.$set(this.list3[i], 'sort', 'top')
							this.$forceUpdate()
							for (var j = 0; j < arry.length; j++) {
								if (arry[j] != i) {
									this.$set(this.list3[j], 'sort', '')
								}
							}
						}
					}
				}
				/* 筛选传参判断 */
				let value;
				if (e.name == "价格") {
					if (e.sort == "button") {
						value = {
							sort_by: 2
						}
					} else {
						value = {
							sort_by: 3
						}
					}
				} else if (e.name == "销量") {
					if (e.sort == "button") {
						value = {
							sort_by: 4
						}
					} else {
						value = {
							sort_by: 5
						}
					}
				} else if (e.name == "利润") {
					if (e.sort == "button") {
						value = {
							sort_by: 8
						}
					} else {
						value = {
							sort_by: 9
						}
					}
				}
				if (this.type == "screen") { //搜索商品页面调用
					this.$store.commit("upSearchGoods", value)
				} else {
					this.$store.commit("upHomePage", value)
				}
				this.$emit("pMerchandiseNews", "tSScreening") //调用父类筛选
			},
			sortSwitch() { //排序切换
				if (this.imgchck == 'icon-fontclass-pailie') {
					this.imgchck = 'icon-fontclass-fenlei'
					this.chck = 'vertical'
				} else {
					this.imgchck = 'icon-fontclass-pailie'
					this.chck = 'crosswise'
				}
			},
			clearValue() { //重置
				let json = {
					price_form: '',
					price_to: '',
					profit_form: '',
					profit_to: '',
				}
				this.$store.commit("upSearchGoods", json)
				for (var i = 0; i < this.list4.length; i++) {
					this.$set(this.list4[i], 'value', '')
					this.$set(this.list4[i], 'value1', '')
					this.$set(this.list4[i], 'name', '最高价')
					this.$set(this.list4[i], 'name1', '最低价')
				}
			},
			goCommodity_details(id) { //跳转到商品详情页面
				this.navTo('/packageA/commodity/commodity_details/commodity_details?id=' + id)
			},
			filtrate() { //筛选
				let json = {
					price_form: this.list4[0].value1,
					price_to: this.list4[0].value,
					profit_form: this.list4[1].value1,
					profit_to: this.list4[1].value
				}
				if (this.checkNull(json.price_form) && this.checkNull(json.price_to) || this.checkNull(json.profit_form) &&
					this.checkNull(json.profit_to)) {
					if (parseInt(json.price_to) <= parseInt(json.price_form) || parseInt(json.profit_to) <= parseInt(json
							.profit_form)) {
						this.showText("最高价必须要高于最低价,请重新输入!")
						return;
					}
				}

				this.show = false
				this.$store.commit("upSearchGoods", json)
				this.$emit("pMerchandiseNews")
			}
		}
	}
</script>

<style scoped>
	.resetBtnCss {
		width: 320rpx;
		height: 62rpx;
		border-radius: 30px 0px 0px 30px;
		border: solid 1px #f14e4e;
		color: #f14e4e;
	}

	.confirmBtnCss {
		width: 320rpx;
		height: 66rpx;
		background-color: #f14e4e;
		border-radius: 0px 30px 30px 0px;
		color: #FFFFFF;
	}

	.swhb2 {
		width: 350rpx;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
	}

	.make {
		background-color: #fde3b7;
		border-radius: 30rpx;
		display: inline-block;
	}

	.bgd5 {
		background-color: #d5d5d5;
		border-radius: 30rpx;
	}

	/deep/.u-border {
		background-color: #ebebeb;
		border: none;
	}
</style>

