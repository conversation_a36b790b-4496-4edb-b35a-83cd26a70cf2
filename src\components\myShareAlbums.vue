﻿<template>
  <!-- 专辑商品页面 -->
  <view>
    <template>
      <view class="item-album">
        <view
          class="album-item bg-f mt_30"
          v-for="(item, index) in list"
          :key="index"
          @click="navTo('/packageC/album/albumDetail?goods_id=' + item.id)"
        >
          <u-image
            v-if="item.covers[0].src"
            :src="item.covers[0].src"
            width="100%"
            height="335rpx"
          ></u-image>
          <u-image v-else width="100%" height="335rpx">
            <u-empty text="暂无图片" mode="data"></u-empty>
          </u-image>
          <view class="pall_25">
            <view class="f fac fjsb">
              <view class="font_size18 font_w700 line-1">{{ item.name }}</view>
              <view class="f fac c-ff7b15">
                <view class="iconfont icon-remai font_size18"></view>
                <view v-if="item.name" class="pt_10 ml_10 font_size18">
                  {{ item.sales_total }}
                </view>
                <view class="c-orange" v-else>仅登录可见</view>
              </view>
            </view>
            <view class="mt_20 pb_10 f fac fjsb">
              <view class="line-1 f1">{{ item.describe }}</view>
              <view class="ml_10">累计销量</view>
            </view>
          </view>
        </view>
        <u-empty
          v-if="list.length === 0"
          mode="list"
          icon="http://cdn.uviewui.com/uview/empty/list.png"
          text="暂无数据"
        ></u-empty>
      </view>
    </template>
  </view>
</template>
<script>
export default {
  name: 'myShareAlbums',
  props: {
    // 加载更多状态
    status: {
      type: String,
      default: () => {
        return 'loadmore'
      },
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
}
</script>
<style lang="scss" scoped>
.c-ff7b15 {
  color: #ff7b15;
}
.pall_25 {
  padding: 10rpx 20rpx;
  background: #fff;
}
.f {
  display: flex;
}
.fac {
  align-items: center;
}
.fjsb {
  justify-content: space-between;
}
.line-1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70vw;
}
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.f1 {
  flex: 1;
}
::v-deep .u-tag__text--warning.data-v-1481d46d {
  padding: 10rpx;
}
</style>

