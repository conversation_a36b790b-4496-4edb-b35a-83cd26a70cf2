﻿<!-- 分类组件 -->
<template>
  <view>
    <!--商品分类-->
    <view class="d-f" v-if="classification != null && classification != ''">
      <!-- 一级分类 -->
      <view
        style="
          width: 22%;
          min-width: 22%;
          color: #666666;
          height: 1230rpx;
          overflow-y: scroll;
          overflow-x: hidden;
        "
        class="pt_15"
      >
        <view
          v-for="(item, index) in classification"
          :key="index"
          class="d-f classifyItem"
          :class="item.checked ? 'seleCI' : ''"
          @click="oneOptions(item, index)"
        >
          <view v-show="item.checked" class="classifyCss"></view>
          <view
            :class="item.checked == true ? 'seleCss' : ''"
            class="fs-2 p-20"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
      <scroll-view
        class="bg-white pr-20 pl-20 pb-20 right-content scroll-Y"
        :scroll-top="scrollTop"
        @scroll="scrollChange"
        scroll-y="true"
        scroll-with-animation="500"
        @scrolltolower="scrolltolower"
      >
        <view style="position: relative">
          <view class="sticky" v-if="!popupIsShow">
            <block>
              <view class="d-bf">
                <!-- 横向二级分类 -->
                <scroll-view
                  scroll-x="true"
                  :scroll-left="scrollTopx"
                  @scroll="scrollChangex"
                  style="white-space: nowrap; width: 93%"
                >
                  <view class="d-cf">
                    <view
                      class="classify2View pt-15 pb-15 pl-30 pr-30 mr-20"
                      :class="c2Index === null ? 'seleCss2' : ''"
                      @click="twoOption({}, null)"
                    >
                      <text>全部</text>
                    </view>
                    <view
                      v-for="(item, index) in classification2"
                      :key="index"
                      class="classify2View pt-15 pb-15 pl-30 pr-30 mr-20"
                      :class="c2Index === index ? 'seleCss2' : ''"
                      @click="twoOption(item, index)"
                    >
                      <text>{{ item.name }}</text>
                    </view>
                  </view>
                </scroll-view>
                <view class="rightView">
                  <u-icon
                    size="25"
                    name="arrow-down"
                    @click="openPopup"
                  ></u-icon>
                </view>
              </view>
              <!-- 排序部分 -->
              <view class="sortView mt-20 pb_20">
                <view class="d-cf">
                  <view
                    v-for="(item, index) in sortList"
                    :key="index"
                    :class="index == 0 ? '' : 'ml-50'"
                    class="d-cc"
                    @click="screen(item)"
                  >
                    <view class="fs-1-5">
                      {{ item.name }}
                    </view>
                    <!-- #ifdef APP-PLUS || H5 -->
                    <view style="transform: scale(0.45)">
                      <u-icon
                        name="arrow-up-fill"
                        :color="item.sort == 'top' ? '#f14d4d' : '#959595'"
                      />
                      <u-icon
                        name="arrow-down-fill"
                        :color="item.sort == 'button' ? '#f14d4d' : '#959595'"
                      />
                    </view>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN-->
                    <view class="d-cc-c ml-5 mt_2">
                      <u-icon
                        name="arrow-up-fill"
                        :color="item.sort == 'top' ? '#f14d4d' : '#959595'"
                        size="13"
                      ></u-icon>
                      <u-icon
                        name="arrow-down-fill"
                        :color="item.sort == 'button' ? '#f14d4d' : '#959595'"
                        size="13"
                      ></u-icon>
                    </view>
                    <!-- #endif -->
                  </view>
                </view>
              </view>
            </block>
          </view>
          <!-- 弹出层分类 -->
          <view class="myClassifyPopup" v-if="popupIsShow">
            <view class="sticky2">
              <view class="c2Sticky2View">
                <!-- 横向二级分类 -->
                <view class="d-f d-wrap">
                  <view
                    class="classify2View pt-15 pb-15 pl-30 pr-30 mr-20"
                    :class="c2Index === null ? 'seleCss2' : ''"
                    @click="twoOption({}, null)"
                  >
                    <text>全部</text>
                  </view>
                  <view
                    v-for="(item, index) in classification2"
                    :key="index"
                    class="classify2View pt-15 pb-15 pl-30 pr-30 mr-20 mb_20"
                    :class="c2Index === index ? 'seleCss2' : ''"
                    @click="twoOption(item, index)"
                  >
                    <text>{{ item.name }}</text>
                  </view>
                </view>
              </view>
              <view class="classyfy3View" v-if="c2Index !== null">
                <view class="mt_40 mb_40">
                  <text>{{ classification2[c2Index].name }}</text>
                </view>
                <view class="classyfy3Item d-f d-wrap">
                  <view
                    v-for="(c3, index) in classification3"
                    :key="index"
                    :class="c3Index === index ? 'seleCss3' : ''"
                    class="pt-15 pb-15 pl-30 pr-30 mr-20 mb_20"
                    @click="threeOption(index)"
                  >
                    <text>{{ c3.name }}</text>
                  </view>
                </view>
              </view>
              <view class="d-c pt_30 pb_30" @click="handleCloseMyPopup">
                <text class="mr_10">点击收起</text>
                <u-icon name="arrow-up" size="24"></u-icon>
              </view>
            </view>
          </view>
          <my-classify-goods
            :user="user"
            ref="classifyGoods"
            :list="productList"
            @updateQTY="updateQTY"
            @openSkus="openSkus"
          ></my-classify-goods>
        </view>
      </scroll-view>
    </view>

    <u-popup
      :show="popupData.isShow"
      :closeOnClickOverlay="false"
      mode="center"
      @close="handleClose"
    >
      <view class="u-popup-slot">
        <view class="d-f">
          <u--image
            :src="popupData.data.image_url"
            width="150"
            height="150"
          ></u--image>
          <text class="ml_20">{{ popupData.data.title }}</text>
        </view>
        <view class="d-f mt_40 flow">
          <view
            v-for="(item, index) in popupData.specificationsSet"
            :key="index"
          >
            <u-tag
              :text="item.title"
              type="error"
              :name="index"
              :plain="item.check"
              @click="specificationIsSelected(index)"
            ></u-tag>
          </view>
        </view>
        <view class="d-bf mt-60">
          <view class="fs-1 font_size16">数量</view>
          <u-number-box v-model="popupData.specificationsNum"></u-number-box>
        </view>
        <u-divider></u-divider>
        <view class="d-bf">
          <text class="c-f14d4d font_size16">
            <text class="font_size12">￥</text>
            {{
              $store.state.userLevelPriceTitle
                ? $store.state.userLevelPriceTitle
                : totalPrice
            }}
          </text>
          <view style="width: 30%">
            <u-button
              type="error"
              shape="circle"
              size="mini"
              text="加入购物车"
              @click="addShoppingCart"
            ></u-button>
          </view>
        </view>
        <view class="myCloseView d-c">
          <u-icon
            name="close-circle"
            color="#fff"
            size="50"
            @click="handleClose"
          ></u-icon>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'myClassifyModule',
  props: {
    url: {
      //type screen为显示筛选
      type: String,
      default: '/api/category/tree',
    },
    type: {
      type: String,
      default: '',
    },
    gather_supply_id: {
      type: Number,
      default: null,
    },
    supplier_id: {
      type: Number,
      default: null,
    },
  },
  computed: {
    totalPrice() {
      const price = this.toYuan(
        this.accMul(
          this.popupData.checkSkus.price,
          this.popupData.specificationsNum,
        ),
      )
      return price
    },
  },
  data() {
    return {
      // skus弹框数据
      popupData: {
        isShow: false,
        data: {},
        specificationsSet: [], // skus
        checkSkus: {}, // 选中的规格
        specificationsNum: 1, // 规格选择数量,
        productID: null,
      },
      user: uni.getStorageSync('user'),
      productList: [],
      sort_by: null,
      classification: [],
      classification2: [],
      classification3: [],
      c2Index: null, // 2级分类选中下标
      c3Index: null, // 3级分类选中下标 只有下拉才会展开三级并且查询三级条件
      popupIsShow: false,
      scrollTop: 0,
      scrollTopx: 0,
      sortList: [
        {
          //二级分类
          name: 'myClassifyModule',
          sort: '',
        },
        {
          name: 'myClassifyModule',
          sort: '',
        },
        {
          name: 'myClassifyModule',
          sort: '',
        },
      ],
      old: {
        scrollTop: 0,
        scrollTopx: 0,
      },
      viewList: ['red', 'yellow', 'gray'],
    }
  },
  mounted() {
    this.$store.commit('upHomeNoMore', false)
    this.$store.commit('upHomePage', {
      //还原筛选状态
      page: 1,
      pageSize: 10,
    })
    this.classifiedData()
  },
  methods: {
    handleClose() {
      this.popupData = {
        isShow: false,
        data: {},
        specificationsSet: [], // skus
        checkSkus: {}, // 选中的规格
        specificationsNum: 1, // 规格选择数量,
        productID: null,
      }
    },
    // 切换规格
    specificationIsSelected(index) {
      for (let i = 0; i < this.popupData.specificationsSet.length; i++) {
        // 添加规格是否选中是否镂空显示
        if (index != i) {
          this.$set(this.popupData.specificationsSet[i], 'check', true)
        }
        this.$set(this.popupData.specificationsSet[index], 'check', false)
        this.popupData.checkSkus = this.popupData.specificationsSet[index]
      }
    },
    // 选规格
    async openSkus(item) {
      this.popupData.isShow = true
      const { data } = await this.getProductData(item.id)
      this.popupData.specificationsSet = data.product.skus
      this.popupData.data = data.product
      this.popupData.checkSkus = this.popupData.specificationsSet[0] // 默认选中第一个规格
      for (let i = 0; i < this.popupData.specificationsSet.length; i++) {
        if (i == 0) {
          this.$set(this.popupData.specificationsSet[i], 'check', false)
        } else {
          this.$set(this.popupData.specificationsSet[i], 'check', true)
        }
      }
    },
    // 获取商品详情
    async getProductData(id) {
      this.popupData.productID = id
      const res = await this.get('/api/product/get', { id }, true)
      return res
    },
    // 加入购物车
    async addShoppingCart() {
      if (this.specificationsNum >= this.popupData.checkSkus.stock) {
        this.showText('选择的数量大于库存啦')
        return
      }
      const params = {
        shopping_carts: [
          {
            qty: this.popupData.specificationsNum,
            sku_id: this.popupData.checkSkus.id,
          },
        ],
      }
      const res = await this.post('/api/shoppingcart/add', params, true)
      if (res.code === 0) {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 2000,
        })
        this.updateQTY({
          qty: this.popupData.specificationsNum,
          productID: this.popupData.productID,
        })
        /* this.$emit('updateQTY', {
          qty: this.popupData.specificationsNum,
          productID: this.popupData.productID,
        }) */
      }
    },
    // 加入购物车后修改列表qty数量
    updateQTY(data) {
      let index = this.productList.indexOfJSON('id', data.productID)
      let qty = this.productList[index].qty + data.qty
      this.$set(this.productList[index], 'qty', qty)
      this.$refs.classifyGoods.handleClose()
    },
    scrollChange(e) {
      this.old.scrollTop = e.detail.scrollTop
    },
    scrollChangex(e) {
      this.old.scrollTopx = e.detail.scrollLeft
    },
    scrolltolower() {
      if (!this.$store.state.homeNoMore) {
        //不是最后一页才获取商品列表
        this.getProductList('refresh')
      }
    },
    // 展开分类
    openPopup() {
      this.popupIsShow = true
      this.classification3 = this.classification2[this.c2Index].children || []
    },
    // 关闭分类
    handleCloseMyPopup() {
      this.popupIsShow = false
    },
    screen(e) {
      let arry = []
      for (var i = 0; i < this.sortList.length; i++) {
        arry.push(i)
      }
      /* 筛选选中状态判断*/
      for (var i = 0; i < this.sortList.length; i++) {
        if (this.sortList[i].name === e.name) {
          if (e.sort == 'top') {
            this.$set(this.sortList[i], 'sort', 'button')
            for (var j = 0; j < arry.length; j++) {
              if (arry[j] != i) {
                this.$set(this.sortList[j], 'sort', '')
              }
            }
          } else {
            this.$set(this.sortList[i], 'sort', 'top')
            this.$forceUpdate()
            for (var j = 0; j < arry.length; j++) {
              if (arry[j] != i) {
                this.$set(this.sortList[j], 'sort', '')
              }
            }
          }
        }
      }
      /* 筛选传参判断 */
      let value
      if (e.name == '价格') {
        if (e.sort == 'button') {
          value = {
            sort_by: 2,
          }
        } else {
          value = {
            sort_by: 3,
          }
        }
      } else if (e.name == '销量') {
        if (e.sort == 'button') {
          value = {
            sort_by: 4,
          }
        } else {
          value = {
            sort_by: 5,
          }
        }
      } else if (e.name == '利润') {
        if (e.sort == 'button') {
          value = {
            sort_by: 8,
          }
        } else {
          value = {
            sort_by: 9,
          }
        }
      }
      this.sort_by = value.sort_by
      this.getProductList()
    },
    oneOptions(item, index) {
      // 一级点击
      // 一级选项
      this.$set(this.classification[index], 'checked', true)
      for (var i = 0; i < this.classification.length; i++) {
        if (i != index) {
          this.$set(this.classification[i], 'checked', false)
        }
      }
      this.c2Index = null

      this.scrollTopx = this.old.scrollTopx
      this.$nextTick(() => {
        this.scrollTopx = 0
      })
      this.classification2 = item.children
      /**
	   * this.popupIsShow = true;
	  this.classification3 = this.classification2[this.c2Index].children || []
	   */
      this.c3Index = null
      if (this.popupIsShow) {
        this.classification3 = this.classification2[this.c2Index].children || []
      }
      this.getProductList()
    },
    twoOption(item, index) {
      // 二级点击
      this.c2Index = index
      this.c3Index = null
      this.classification3 = item?.children
      this.getProductList()
    },
    // 三级点击
    threeOption(index) {
      this.c3Index = index
      this.getProductList()
    },
    classifiedData() {
      //分类数据
      this.get(this.url, {}, true).then(data => {
        if (this.type) {
          this.classification = data.data
          this.classification2 = this.classification[0].children
        } else {
          this.classification = data.data.categories
          this.classification2 = this.classification[0].children
        }

        /* 一级选项 */
        for (var i = 0; i < this.classification.length; i++) {
          if (i == 0) {
            this.$set(this.classification[i], 'checked', true)
          } else {
            this.$set(this.classification[i], 'checked', false)
          }
        }
        this.getProductList()
      })
    },
    // 获取商品
    getProductList(stair) {
      let url = '/api/product/list'
      if (uni.getStorageSync('user')) url = '/api/product/listLogin'
      if (stair != 'refresh') {
        //除了下拉刷新以外所有的筛选都是从第一页开始
        this.$store.commit('upHomePage', {
          page: 1,
        })
        this.scrollTop = this.old.scrollTop
        this.$nextTick(() => {
          this.scrollTop = 0
        })
      }
      let json = this.$store.state.homePage //要传的参数
      let params = {
        page: json.page,
        pageSize: json.pageSize,
      }
      if (this.supplier_id !== null) {
        params.supplier_id = this.supplier_id
      }
      if (this.gather_supply_id !== null) {
        params.gather_supply_id = this.gather_supply_id
      }
      if (this.classification && this.classification.length > 0) {
        // 一级分类
        let c1Obj = this.classification.find(item => item.checked === true)
        if (c1Obj) params.category1_id = c1Obj.id
      }
      if (this.c2Index !== null) {
        // 二级分类
        let c2Obj = this.classification2[this.c2Index]
        if (c2Obj) params.category2_id = c2Obj.id
      }
      if (this.c3Index !== null) {
        // 二级分类
        let c3Obj = this.classification3[this.c3Index]
        if (c3Obj) params.category3_id = c3Obj.id
      }
      if (this.sort_by !== null) params.sort_by = this.sort_by
      this.get(url, params, true).then(async res => {
        let cartData = []
        if (uni.getStorageSync('user')) {
          let cartRes = await this.getShoppingCartData()
          cartData = cartRes.data.shopping_carts
        }
        for (var i = 0; i < res.data.list.length; i++) {
          //分转元
          /* res.data.list[i].price = this.toYuan(res.data.list[i].price);
          res.data.list[i].origin_price = this.toYuan(
            res.data.list[i].origin_price
          ); */
          if (cartData && cartData.length) {
            res.data.list[i].qty = 0
            cartData.forEach(item => {
              if (item.product.id === res.data.list[i].id) {
                res.data.list[i].qty += item.qty
              }
            })
          }
        }
        //判断是不是最后一页
        if (res.data.page < res.data.total / res.data.pageSize) {
          this.$store.commit('upHomeNoMore', false)
          this.$store.commit('upHomePage', {
            page: this.$store.state.homePage.page + 1,
          })
        } else {
          this.$store.commit('upHomeNoMore', true)
        }
        if (stair == 'refresh') {
          this.productList = [...this.productList, ...res.data.list]
        } else {
          this.productList = res.data.list
        }
      })
    },
    // 获取购物车数据
    async getShoppingCartData() {
      let res = await this.post('/api/shoppingcart/list', { checked: 0 }, true)
      return res
    },
  },
}
</script>
<style scoped lang="scss">
.classifyItem {
  margin-top: 20rpx;
  &:first-child {
    margin-top: 0;
  }
  &.seleCI {
    background-color: rgba($color: #f15353, $alpha: 0.1);
  }
  .classifyCss {
    background-color: #f15353;
    width: 6rpx;
  }
  .seleCss {
    color: #f15353;
  }
}
.right-content {
  width: 50%;
  flex: 1;
  .seleCss2 {
    color: #f15353;
    background-color: rgba($color: #f15353, $alpha: 0.1);
  }
}
.scroll-Y {
  height: 100vh;
}
.sticky {
  position: sticky;
  position: -webkit-sticky;
  z-index: 98;
  top: 0;
  width: 100%;
  background-color: #fff;
  .classify2View {
    border-radius: 40rpx;
    background-color: #f8f8f6;
    &:last-child {
      margin-right: 0;
    }
  }
  .rightView {
    width: 30rpx;
  }
}
// 自定义popup
.myClassifyPopup {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  z-index: 99;
  background-color: rgba($color: #000000, $alpha: 0.6);
  .sticky2 {
    position: sticky;
    position: -webkit-sticky;
    z-index: 100;
    top: 0;
    width: 100%;
    background-color: #fff;
    border-bottom-right-radius: 20rpx;
    .c2Sticky2View {
      max-height: 400rpx;
      overflow: auto;
      .classify2View {
        border-radius: 40rpx;
        background-color: #f8f8f6;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .classyfy3View {
      .classyfy3Item {
        max-height: 200rpx;
        overflow: auto;
        .seleCss3 {
          border: 1px solid #f15353;
          color: #f15353;
        }
      }
    }
  }
}
.u-popup-slot {
  width: 80vw;
  padding: 50rpx 30rpx;
  position: relative;
  .myCloseView {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -140rpx;
  }
}
</style>
