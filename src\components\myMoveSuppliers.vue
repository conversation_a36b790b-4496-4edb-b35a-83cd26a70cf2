﻿<template>
    <view>
        <view v-for="itemList in list" :key="itemList.id" class="m-20 p-20 bsbb radius15 bg-white">
            <view class="f fac mb-20" @click="goStore(itemList.id, 0)">
                <u--image :showLoading="true" radius="16" height="88rpx" width="88rpx"
                    :src="itemList.shop_logo ? itemList.shop_logo : logo_img">
                </u--image>
                <view class="f1 ml-15">
                    <view class="f fac fjsb">
                        <view class="title limit-text-1 f1 fs-2-5">{{ itemList.shop_name }}</view>
                        <view v-if="itemList.category_info.name" class="color80 fs-1-5 ml-25 pl-10 pr-10 f fac fjc bg-f5" style="height: 40rpx;border-radius: 8rpx;">
                            {{ itemList.category_info.name }}
                        </view>
                    </view>
                    <view class="f fac fs-1-5 mt-10 color80">
                        商品数量：<span class="c-orange mr-25">{{ itemList.goods_count }}</span>
                        热销（件）：<span class="c-orange">{{ itemList.hot_sale }}</span>
                    </view>
                </view>
            </view>
            <u-grid :border="false" :col="3" class="mt-20">
                <template v-for="product in cutOut(itemList.product)">
                    <u-grid-item :key="product.id" @click="goCommodity(product.id)">
                        <u--image :showLoading="true" width="210rpx" height="210rpx" radius="16" :src="product.thumb">
                        </u--image>
                        <text class="limit-text-1 mt-20 title" style="width:210rpx">
                            {{ product.title }}
                        </text>
                        <text class="limit-text-1 mt-15 c-orange fs-2 fw-b" style="width:210rpx" v-if="checkNull(user)">
                            ¥{{ toYuan(product.price) }}
                        </text>
                        <text class="c-orange fs-2"  style="width:210rpx" v-else>价格登录可见</text>
                    </u-grid-item>
                </template>
            </u-grid>
        </view>
    </view>
</template>

<script>
export default {
    name: 'myMoveSuppliers',
    props: {
        list: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    data() {
        return {
            user: uni.getStorageSync('user'),
            logo_img: uni.getStorageSync('h5_logo'),
        }
    },
    methods: {
        // 进入商品详情
        goCommodity(id) {
            this.navTo('/packageA/commodity/commodity_details/commodity_details?id=' + id)
        },
        cutOut(arr) {
            let newArr = arr
            if (arr && arr.length > 4) {
                newArr = arr.splice(4)
            }
            return newArr
        },
        // 进入店铺
        goStore(id, type) {
            this.navTo("/packageB/store/storeDetails?id=" + id + "&type=" + type)
        },
    }
}
</script>
<style lang="scss" scoped>
.title {
    color: #00001C;
    font-weight: bold;
    font-size: 28rpx;
}

.color80 {
    color: #808080;
}

.limit-text-1 {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.bsbb {
    box-sizing: border-box;
}
</style>
