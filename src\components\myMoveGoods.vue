﻿<template>
    <view>
        <view v-for="itemList in list" :key="itemList.id" class="m-20 radius15 p-20 bsbb bg-white f fac" style="height: 260rpx;"  @click="goCommodity(itemList.id)">
            <u--image :showLoading="true" width="220rpx" height="220rpx" radius="16" :src="itemList.thumb">
            </u--image>
            <view class="ml-15" style="height: 220rpx;width: 435rpx;">
                <view class="limit-text-1 title mt-10">{{ itemList.title }}</view>
                <view class="f fac fjsb mt-10">
                    <view>
                        <view class="c-f1 fs-1" v-if="page_setup_setting.pc_list_setting.is_web_price === 0">
                            {{ page_setup_setting.language_setting.price === '' ? '批发价' :
                                page_setup_setting.language_setting.price }}
                            <span v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)">￥ {{
                                toYuan(itemList.normal_price) }}</span>
                            <span v-else-if="userLevelPriceTitle">{{ ' ￥' + userLevelPriceTitle }}</span>
                            <span v-else>{{ checkNull(user) ? ' ￥' +
                                toYuan(itemList.normal_price) : ' 价格登录可见'
                                }}</span>
                        </view>
                        <view class="c-f1 mt-10 fs-1">
                            <span v-if="userLevelPriceTitle && isMatchingPriceAuthority(itemList.id)">
                                {{ '利润 ¥' + toYuan(itemList.level_profit) + '/件' }}
                            </span>
                            <span v-else-if="userLevelPriceTitle">{{ '利润 ￥' + userLevelPriceTitle + '/件'
                                }}</span>
                            <span v-else>{{ checkNull(user) ? '利润 ￥' +
                                toYuan(itemList.level_profit) : '利润 登录可见'
                                }}</span>
                        </view>
                    </view>
                    <view>
                        <view class="c-gray4 fs-1">已售 {{ itemList.sales }}</view>
                        <view class="c-gray4 mt-10 fs-1">库存 {{ itemList.stock }}</view>
                    </view>
                </view>
                <!-- 超级批发价 和 建议零售价 按钮-->
                <view @click="goCommodity(itemList.id)"
                    v-if="page_setup_setting.pc_list_setting.is_web_super_wholesal_price === 0 && page_setup_setting.pc_list_setting.is_web_suggested_retail_price === 0"
                    class="f fac mt-20 button-view" style="background-color: rgba(255, 116, 57, 0.2);">
                    <view class="trapezoid-left d-cc-c">
                        <view class="fs-2 c-f1 fw-b"
                            v-if="userLevelPriceTitle && isMatchingPriceAuthority(itemList.id)">{{ '¥' +
                                toYuan(itemList.origin_price) }}</view>
                        <view class="fs-2 c-f1 fw-b" v-else-if="userLevelPriceTitle">{{ '¥' +
                            userLevelPriceTitle }}</view>
                        <view class="fs-2 c-f1 fw-b" v-else="userLevelPriceTitle">{{
                            toYuan(itemList.origin_price) }}</view>
                        <text class="fs-1 c-f1">{{
                            page_setup_setting.language_setting.suggested_retail_price === '' ? '建议零售价' :
                                page_setup_setting.language_setting.suggested_retail_price }}</text>
                    </view>
                    <view class="trapezoid-right d-cc-c">
                        <view class="fs-2 c-white fw-b"
                            v-if="userLevelPriceTitle && isMatchingPriceAuthority(itemList.id)">{{ '¥' +
                                toYuan(100010) }}</view>
                        <view class="fs-2 c-white fw-b" v-else-if="userLevelPriceTitle">{{ '¥' +
                            userLevelPriceTitle }}</view>
                        <view class="fs-2 c-white fw-b" v-else>{{
                            checkNull(user) ? '¥' + toYuan(itemList.min_price) : '价格登录可见' }}</view>
                        <text class="fs-1 c-white">{{
                            page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' :
                                page_setup_setting.language_setting.super_wholesal_price }}</text>
                    </view>
                </view>
                <!-- 建议零售价 按钮 -->
                <view v-else-if="page_setup_setting.pc_list_setting.is_web_suggested_retail_price === 0"
                    class="f fac mt-20 button-view" style="background-color: rgba(255, 116, 57, 0.2);"
                    @click="goCommodity(itemList.id)">
                    <view class="d-cc-c" style="width: 100%;height: 100%;">
                        <view class="fs-2 c-f1 fw-b"
                            v-if="userLevelPriceTitle && isMatchingPriceAuthority(itemList.id)">{{ '¥' +
                                toYuan(itemList.origin_price) }}</view>
                        <view class="fs-2 c-f1 fw-b" v-else-if="userLevelPriceTitle">{{ '¥' +
                            userLevelPriceTitle }}</view>
                        <view class="fs-2 c-f1 fw-b" v-else="userLevelPriceTitle">{{
                            toYuan(itemList.origin_price) }}</view>
                        <text class="fs-1 c-f1">{{
                            page_setup_setting.language_setting.suggested_retail_price === '' ? '建议零售价' :
                                page_setup_setting.language_setting.suggested_retail_price }}</text>
                    </view>
                </view>
                <!-- 超级批发价 按钮 -->
                <view v-else-if="page_setup_setting.pc_list_setting.is_web_super_wholesal_price === 0"
                    class="f fac mt-20 button-view" @click="goCommodity(itemList.id)">
                    <view class="d-cc-c"
                        style="background: linear-gradient(135deg, #FF8740 0%, #FF1616 100%);width: 100%;border-radius: 12rpx;height: 100%;">
                        <view class="fs-2 c-white fw-b"
                            v-if="userLevelPriceTitle && isMatchingPriceAuthority(itemList.id)">{{ '¥' +
                                toYuan(itemList.min_price) }}</view>
                        <view class="fs-2 c-white fw-b" v-else-if="userLevelPriceTitle">{{ '¥' +
                            userLevelPriceTitle }}</view>
                        <view class="fs-2 c-white fw-b" v-else>{{
                            checkNull(user) ? '¥' + toYuan(itemList.min_price) : '价格登录可见' }}</view>
                        <text class="fs-1 c-white">{{
                            page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' :
                                page_setup_setting.language_setting.super_wholesal_price }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'myMoveGoods',
    props: {
        list: { //商品信息
            type: Array,
            default: () => {
            return []
        },
        },
    },
    computed: {
        userLevelPriceTitle() {
            return this.$store.state.userLevelPriceTitle || ""
        },
    },
    data() {
        return {
            page_setup_setting: { // 商品价格显示名称
                pc_list_setting: {},
                details_setting: {},
                language_setting: {}
            },
            user: uni.getStorageSync('user')
        }
    },
    created() {
        this.get('/api/home/<USER>', {}, true).then(data => {
            this.page_setup_setting = data.data.page_setup_setting
        })
    },
    methods: {
        jumpLogin() {
            if (this.checkWenxin() && !this.checkNull(this.user)) {
                // 公众号登录
                this.isWeiXinBrowser()
                return;
            }
            /* if (!this.user) {
                this.navTo("/pages/login/login")
            } */
        },
        // 进入商品详情
        goCommodity(id) {
            this.navTo('/packageA/commodity/commodity_details/commodity_details?id=' + id)
        },
    }
}
</script>

<style scoped>
.limit-text-1 {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.title {
	color: #00001C;
	font-weight: bold;
	font-size: 28rpx;
}

.bsbb {
    box-sizing: border-box;
}


.button-view {
    width: 426rpx;
    height: 77rpx;
    position: relative;
    border-radius: 12rpx;
}

.trapezoid-left,
.trapezoid-right {
    position: absolute;
    height: 100%;
}

.trapezoid-left {
    width: 196rpx;
    border-radius: 12rpx;
}

.trapezoid-right {
    width: 230rpx;
    background: linear-gradient(135deg, #FF8740 0%, #FF1616 100%);
    right: 0;
    clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
    border-radius: 12rpx;
}
</style>
