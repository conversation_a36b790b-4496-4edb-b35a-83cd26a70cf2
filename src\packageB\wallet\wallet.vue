﻿<!-- 我的钱包 -->
<template>
	<view id="wallet">
		<view class="walletTitle">
			<view class="c-333 f-bold">Hi，欢迎回来！</view>
		</view>
		<!-- 汇聚余额与站内余额 start-->
		<view class="balance not-mt converge-bg" v-if="isRecharge === 1 && balance_settings.join_balance !=='2' || balance_settings.station_balance !=='2'">
			<view class="balance-wrap d-cf" >
				<!-- <view class="converge" v-if="balance_settings.join_balance === '1'">
					<view class="title">汇聚余额</view>
					<view class="money f-bold">{{Number(balanceTypeOne.converge_balance).toFixed(2) || 0.00}}</view>
				</view> -->
				<view class="converge" v-if="balance_settings.station_balance === '1'">
					<view class="title">站内余额</view>
					<view class="money f-bold">{{Number(balanceTypeOne.purchasing_balance).toFixed(2) || 0.00}}</view>
				</view>
			</view>
			<!-- 汇聚余额充值 start -->
			<!-- <view 
				class="balance-cell" 
				v-if="balance_settings.join_recharge === '1' && balance_settings.join_balance !== '2'" 
				@click="navTo('/packageB/member/recharge?pay_type=1')"
			>
				<view class="d-bf cell">
					<view class="c-333 font_size14">汇聚余额充值</view>
					<view class="iconfont icon-member_right"></view>
				</view>
			</view> -->
			<!-- 汇聚余额充值 end -->
			<!-- 站内余额充值 start -->
			<view 
				class="balance-cell" 
				v-if="balance_settings.station_recharge === '1' && balance_settings.station_balance !== '2'" 
				@click="navTo('/packageB/member/recharge?pay_type=2')"
			>
				<view class="d-bf cell">
					<view class="c-333 font_size14">站内余额充值</view>
					<view class="iconfont icon-member_right"></view>
				</view>
			</view>
			<!-- 站内余额充值 end -->
		</view>
		<view class="balance not-mt account-bg" v-if="balanceTypeTwo.type === 2 && status">
			<view class="balance-wrap d-cf">
				<view class="converge">
					<view class="title">站内结算余额</view>
					<view class="money f-bold">{{Number(balanceTypeTwo.settlement_balance).toFixed(2) || 0.00}}</view>
				</view>
			</view>
			<view class="balance-cell">
				<view class="d-bf cell" @click="navTo('/packageB/member/withdraw?withdrawal_type=1')">
					<view class="c-333 font_size14">提现</view>
					<view class="iconfont icon-member_right"></view>
				</view>
				<view class="d-bf cell" @click="navTo('/packageB/member/bindBank')">
					<view class="c-333 font_size14">绑定银行卡</view>
					<view class="iconfont icon-member_right"></view>
				</view>
			</view>
		</view>
		<!-- 汇聚余额与站内余额 end-->
		<!-- 收入余额 start-->
		<view class="balance income-bg">
			<view class="balance-wrap d-cf">
				<view class="converge">
					<view class="title">收入余额</view>
					<view class="money f-bold">{{Number(income_amount).toFixed(2)}}</view>
				</view>
			</view>
			<view class="balance-cell">
				<view class="d-bf cell" @click="navTo('/packageB/member/withdraw?withdrawal_type=2')">
					<view class="c-333 font_size14">提现</view>
					<view class="iconfont icon-member_right"></view>
				</view>
				<view class="d-bf cell" @click="navTo('/packageB/member/bindBank')">
					<view class="c-333 font_size14">绑定银行卡</view>
					<view class="iconfont icon-member_right"></view>
				</view>
			</view>
		</view>
		<!-- 收入余额 end-->
		<view class="hint">温馨提示：提现到账时间三到五个工作日，节假日顺延；</view>
		<!-- 各种充值的查询记录列表 start-->
		<view class="wallet-content">
			<view class="wallet-tab-bg">
				<view class="wallet-tab ">
					<u-tabs
						lineWidth="105rpx" 
						:current="tabIndex" 
						:lineHeight='walletTab.length' 
						:list="walletTab"
						@click="tabClick"
						lineColor="#f56c6c"
						ellipsis="false"
						:activeStyle="{
							color: '#f14e4e',
						}" 
						:inactiveStyle="{
							color: '#0c0d0e',
						}"
						itemStyle="height: 80rpx;text-align:center;background:#fff;font-size:26rpx; padding:0 30rpx;">
					</u-tabs>
				</view>
				<view v-if="tabIndex == 0" class="mt_20">
					<view class="f fac">
						<uni-datetime-picker class="date-time" type="datetime" v-model="times"></uni-datetime-picker>
						<view class="ml_15 mr_15">至</view>
						<uni-datetime-picker class="date-time" type="datetime" v-model="timee"></uni-datetime-picker>
					</view>
					<view class="f fac mt_15">
						<uni-data-select class="select select0p" v-model="business_type" :localdata="business_list" :clear="true" style="padding: 0rpx;margin-right: 50rpx" placeholder="业务类型"></uni-data-select>
						<u-button class="search" color="#F32321" @click="searchList">搜索</u-button>
					</view>
				</view>
			</view>
			<!-- <view class="select d-ef" @click="useOutClickSide" v-if="tabIndex === 1">
					<my-easy-select 
						ref="easySelect" 
						style="width: 165rpx;height: 46rpx;font-size: 24rpx;color:#494949; padding-right:0;" 
						:value="selectValue" 
						@selectOne="selectOne"
						>
					</my-easy-select>
			</view>	 -->		
			<!-- 汇聚余额充值记录 -->
			<recharge-list v-show="tabIndex === 1" :rechargeList="rechargeLists"></recharge-list>
			<!-- 提现记录 -->
			<record-list @handleTabChange="handleTabChange" :statur="formData.status" :recordList="recordList" v-if="tabIndex === 2"></record-list>
			<!-- 已绑定的银行卡 -->
			<view class="wallet-list-bank" v-if="tabIndex === 3">
				<u-radio-group placement="column" v-model="cardId" v-if="cardListBank.length > 0">		
					<block v-for="(item,index) in cardListBank" :key="index" >
						<view class="mb_20 card-content">
							<view class="item d-bf2" >
								<view class="d-f-c font_size12 c-29">
									<!-- 渲染银行卡-->
										<view class="mb_26">{{item.bank_name}}</view>
										<view class="mb_26">{{item.bank_account}}</view>
								</view>
								<view class="d-f-c text-r font_size12">
									<view class="mb_26 c-74" >{{item.account_name}}</view>
								</view>
							</view>
							<view class="d-bf action">
								<view class="d-cf font_size12 c-74">
									<view class="action-del" @click="cardDel(item.id)">
										<text class="iconfont icon-ht_operation_delete"></text>
										<text>删除</text>
									</view>
									<view @click="navTo('/packageB/member/bindBank?id=' + item.id)">
										<text class="iconfont icon-fontclass-xiugai"></text>
										<text>编辑</text>
									</view>
								</view>
								<view class="d-cf">
									<u-radio
											shape="square"
											size="20"
											:name="item.id"
											activeColor="#f14e4e"
											@change="radioChangeBank(item.is_default,item.id)"
									>
									</u-radio>
									<text class="font_size12" >设置默认</text>
								</view>
							</view>
						</view>
					</block>
				</u-radio-group>
				<view v-else  class="white-pb bg-white">
					<u-empty mode="data" >
					</u-empty>
				</view>
			</view>
			<withdraw-list v-if="tabIndex === 0" :withdrawList="walletList" @withdrawItem="onWithdrawItem"></withdraw-list>
			<!-- 收入明细 -->
			<income-list v-if="tabIndex === 4" :incomeList="incomeList"></income-list>
		</view>
		<!-- 各种充值的查询记录列表 end-->
		<view class="mb24"></view>
		<!-- <u-loadmore :status="loadStatus"  stlye="height:30rpx;"/> -->
	</view>
</template>
<script>
					export default {
				name:'Wallet',
		data() {
			return {
				times: '', // 起始时间
				chooseTimes: '',
				timee: '', // 结束时间
				chooseTimee: '',
				business_list: [
					{
						text: "汇聚余额采购",
						value: 1,
					},
					{
						text: "站内余额采购",
						value: 2,
					},
					{
						text: "汇聚余额充值",
						value: 3,
					},
					{
						text: "站内余额充值",
						value: 4,
					},
					{
						text: "站内余额退款",
						value: 5,
					},
					{
						text: "站内余额扣除",
						value: 6,
					},
				],
				business_type: '', // 选中的类型
				scroll_left:0  ,
				cardId:0, //银行卡ID，编辑新增
				tabIndex:0,
				card:'card',
				balanceShow:false,
				status:0, //申请代理商状态
				balanceItem:{},
				income_amount:0,
				loadStatus: 'loadmore',
				balanceList:[],
				balanceTypeOne:{
					type:null,
					purchasing_balance:0,
					converge_balance:0
				},
				balanceTypeTwo:{
					type:null,
					settlement_balance:0,
				},
				isRecharge: 1, //1开，2关
				balance_settings:{
					join_balance: 0, //汇聚余额:没有2 选中1 不选就空
					join_recharge: 0, // 汇聚充值
					station_balance: 0, // 站内余额
					station_recharge: 0 // 站内充值
					// join_balance: "1", //汇聚余额:没有2 选中1 不选就空
					// join_recharge: "1", // 汇聚充值
					// station_balance: "2", // 站内余额
					// station_recharge: "2" // 站内充值
				},
				total:null, //总共多少条数据
				selectStatus:null, //打款的状态
				formData:{
					pageSize:10,  //每页10条数据
					page:1,   //第几页
					withdrawal_status:null,
					remit_status:null,
					status: null
				},
				walletPage:1,
				walletpageSize:10,
				walletTab1:[
					{
						name: '余额明细',
					},
					/* {
						name: '汇聚余额充值记录',
					}, */
					{
						name: '提现记录',
					},
					{
						name: '已绑定的银行卡',
					},
					/* {
						name: '余额明细',
					}, */
					{
						name: '收入明细',
					},
				],
				selectValue: '提现状态',
				walletList: [], //获取余额明细
				type_list: [],
				cardListBank:[], //银行卡列表
				recordList:[], //提现记录
				incomeList:[],
				rechargeLists:[
					// {
					// 		"id": 120,
					// 		"created_at": **********,
					// 		"updated_at": **********,
					// 		"pay_sn": *************,
					// 		"uid": 215,
					// 		"amount": 10,
					// 		"remaining_amount": 10,
					// 		"pay_type": 1,
					// 		"pay_at": **********,
					// 		"pay_status": 1,
					// 		"page": 0,
					// 		"pageSize": 0
					// }
				] //汇聚余额充值记录
			}
		},
		computed:{
			walletTab: function () {
				if(this.balance_settings.join_balance === '2'){
					this.walletTab1.splice(1,1)
					//this.walletTab1[4].name = '汇聚余额充值记录'
				}
				if(this.balance_settings.station_balance === '1'){
					//this.walletTab1[4].name = '站内余额充值记录'
				}
				return this.walletTab1
			}
    },
		onLoad(e){
			console.log(e,'e');
			if (e.statur) {
				console.log(1);
				switch (e.statur) {
					case '1':
					    this.tabIndex = 4
 						break;
					case '2':
					    this.tabIndex = 2
						this.formData.status = 4
 						break;
					case '3':
					    this.tabIndex = 2
						this.formData.status = 1
 						break;
					case '4':
					    this.tabIndex = 2
						this.formData.status = 1
 						break;
					default:
						break;
				}
			}
		},
		onShow() {
			["walletList","recordList","incomeList","rechargeLists"].forEach((key) => {
				this[key] = []
			});
			this.supplierStatus(); //获取供应商申请状态
			this.getUserBalance(); //获取用户余额
			this.getUserBankList(); //获取银行卡列表
			this.getUserIncome();//获取收入
			// this.getWithdrawList(); //获取提现记录
			this.getPurchaseBalanceList(); //获取余额明细
			// this.getIncomeDetailList(); //获取收入列表
			this.getUserRecharge(); //获取充值记录
			this.initUserInfo() //获取用户信息
		},
		onHide() {

		},
		onReachBottom(){
			let  allTotal = this.formData.page * this.formData.pageSize;
			let  walletTotal = this.walletPage * this.walletpageSize;
				if(allTotal < this.total && this.tabIndex === 2){
					this.loadStatus = 'loading';  //加载中状态
					//当前条数小于总条数 则增加请求页数
					this.formData.page ++;
					this.getWithdrawList();
				}else{
					this.loadStatus = 'nomore'; //加载完状态
					// console.log('已加载全部数据')
				}
				if(walletTotal < this.walletTotal  && this.tabIndex === 1) {
					this.loadStatus = 'loading';  //加载中状态
					this.walletPage ++;
					this.getPurchaseBalanceList();
					this.getIncomeDetailList(); //获取收入列表
					this.getUserRecharge(); //获取充值记录
				} else {
					this.loadStatus = 'nomore'; //加载完状态
				}
		},
		onPullDownRefresh() {
			["walletList","recordList","incomeList","rechargeLists"].forEach((key) => {
				this[key] = []
			});
			this.formData.page = 1;
			this.walletPage = 1;
			//调用获取数据方法
			// this.getWithdrawList();
			// this.getPurchaseBalanceList();
			// this.getIncomeDetailList(); //获取收入列表
			// this.getUserRecharge(); //获取充值记录
			this.tabSwitch(this.tabIndex);
			setTimeout(() => {
				//结束下拉刷新
			  uni.stopPullDownRefresh();
			}, 1000);
		},
		// 触底加载事件
		onReachBottom() {
			if(this.walletList.length < this.walletTotal && this.tabIndex == 0 ) {
				this.walletPage = this.walletPage + 1
				this.get(`/api/finance/getPurchaseBalanceList?page=${this.walletPage}&pageSize=${this.walletpageSize}&business_type=${this.business_type}&times=${this.chooseTimes}&timee=${this.chooseTimee}`, {}, true).then((res) => {
						if(res.code === 0) {
							let data = res.data;
							let  newlist = data.list;
							this.type_list = data.type_list;
							this.walletTotal = data.total; //余额明细记录总数newlist[i]
							for(let i = 0; i<newlist.length;i++) {
								newlist[i].amount = Number(this.toYuan(newlist[i].amount));
								newlist[i].balance = Number(this.toYuan(newlist[i].balance));
								newlist[i].created_at = this.formatDateTime(newlist[i].created_at,6);
								let item = this.type_list.find((item) => {
									return item.value === newlist[i].business_type
								})
								if(item){
									this.$set(newlist[i], 'title',  item.name);
								}
							}
							this.walletList.push(...newlist);
						} else {
							// this.toast(res.msg);
						}
					})
			}
		},
		methods: {
			handleTabChange(key){
				console.log(key);
				this.formData.status = key
				this.getWithdrawList()
			},
			//获取用户信息
			initUserInfo() {
					this.post("/api/center/index").then(res => {
						if (res.code == 0) {
							this.balance_settings = res.data.balance_settings
							this.isRecharge = 1
							if(
								res.data.balance_settings.join_balance === '2' &&
								res.data.balance_settings.join_recharge === '2' &&
								res.data.balance_settings.station_balance === '2' &&
								res.data.balance_settings.station_recharge === '2' 
							){
								this.isRecharge = 2
							}
						}
					}).catch(function (res) {
						console.log(res);
					});
			}, 
			tabClick(item) {
				this.formData.page = 1;
				this.walletPage = 1;
				["walletList","recordList","incomeList","rechargeLists"].forEach((key) => {
					this[key] = []
				});
				this.tabIndex = item.index;
				this.tabSwitch(this.tabIndex);
			},
			tabSwitch(itemIndex) {
				switch (itemIndex) {
					case 0:
						this.getPurchaseBalanceList(); //余额明细
						break
					case 1:
						this.getUserRecharge(); //汇聚余额充值记录
						break
					case 2:
						this.getWithdrawList(); //提现记录
						break
					case 3:
						this.getUserBankList(); //已绑定银行卡
						break
					case 4:
						this.getIncomeDetailList(); //收入明细
						break
				}
			},
			selectOne(options) {
					this.selectValue = options.label
					this.selectStatus = options.value;
					if(this.selectStatus === 0) {
						this.formData.withdrawal_status = 0;
						this.formData.remit_status = null;
						
					} else if(this.selectStatus === 5) { //待打款
						this.formData.remit_status = 0;
						this.formData.withdrawal_status = null;
						
					} else if (this.selectStatus === 3) {
						this.formData.remit_status = 3;
						this.formData.withdrawal_status = null;
						
					} else if (this.selectStatus === 1) {
						this.formData.remit_status = 1;
						this.formData.withdrawal_status = null;

					} else {
						this.formData.withdrawal_status = null;
						this.formData.remit_status = null;
					}
					this.recordList = [];
					this.getWithdrawList();
			},
			useOutClickSide() {
					this.$refs.easySelect.hideOptions && this.$refs.easySelect.hideOptions()
			},
			balanceClose(data) {
				this.balanceShow = data;
			},
			onWithdrawItem(item) {
				this.balanceShow = true;
				this.balanceItem = item;
			},
			radioChangeBank(is_default,id) { //修改默认选择地址
				is_default = 1;
				this.post('/api/finance/saveUserBank', {is_default,id}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						uni.$u.toast(res.msg);
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			cardDel(id) {
				console.log(id);
				this.post('/api/finance/deleteUserBank', {id}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						uni.$u.toast(res.msg);
						this.cardId = 0;
						setTimeout(() => {
							this.getUserBankList();
						},500)
						
						
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			getUserIncome() { // 收入信息
				this.post('/api/finance/getUserIncome', {}, true,true,false).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.income_amount = this.toYuan(data.income_amount);
					} else {
						// this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			getWithdrawList() {
				this.post('/api/finance/getWithdrawList', this.formData, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let  recordList = data.list;
						for(let i in recordList) {
							recordList[i].income_amount = this.toYuan(recordList[i].income_amount);
							recordList[i].withdrawal_amount = this.toYuan(recordList[i].withdrawal_amount);
							recordList[i].service_tax = this.toYuan(recordList[i].service_tax);
							recordList[i].poundage_amount = this.toYuan(recordList[i].poundage_amount);
							recordList[i].created_at = this.formatDateTime(recordList[i].created_at,6);
						}
						this.recordList.push(...recordList);
						this.total = data.total;
					} else {
						// this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			//获取收入列表
			getIncomeDetailList() { 
				this.post('/api/finance/getIncomeDetailList', {
					page: this.walletPage,
					pageSize: this.walletpageSize
				}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let  incomeList = data.list;
						for(let i in incomeList) {
							incomeList[i].amount = this.toYuan(incomeList[i].amount);
							incomeList[i].balance = this.toYuan(incomeList[i].balance);
							incomeList[i].created_at = this.formatDateTime(incomeList[i].created_at,6);
						}	
						this.incomeList.push(...incomeList);
					} else {
						// this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			//汇聚余额充值记录
			getUserRecharge() { 
				this.post('/api/finance/getUserRecharge', {
					page: this.walletPage,
					pageSize: this.walletpageSize
				}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let rechargeLists = data.list;
						for(let i in rechargeLists) {
							rechargeLists[i].amount = Number(this.toYuan(rechargeLists[i].amount));
							rechargeLists[i].remaining_amount = Number(this.toYuan(rechargeLists[i].remaining_amount));
							rechargeLists[i].created_at = this.formatDateTime(rechargeLists[i].created_at,6);
						}
						this.rechargeLists.push(...rechargeLists);
					} else {
						// this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			//余额明细
			getPurchaseBalanceList() { 
				this.get(`/api/finance/getPurchaseBalanceList?page=${this.walletPage}&pageSize=${this.walletpageSize}&business_type=${this.business_type}&times=${this.chooseTimes}&timee=${this.chooseTimee}`, {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						let  newlist = data.list;
						this.type_list = data.type_list;
						this.walletTotal = data.total; //余额明细记录总数newlist[i]
						for(let i = 0; i<newlist.length;i++) {
							newlist[i].amount = Number(this.toYuan(newlist[i].amount));
							newlist[i].balance = Number(this.toYuan(newlist[i].balance));
							newlist[i].created_at = this.formatDateTime(newlist[i].created_at,6);
							let item = this.type_list.find((item) => {
								return item.value === newlist[i].business_type
							})
							if(item){
								this.$set(newlist[i], 'title',  item.name);
							}
						}
						this.walletList.push(...newlist);
					} else {
						// this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			supplierStatus() {
				this.get('/api/supplier/getSupplierApplyStatus', {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.status = data.restatus;
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
			getUserBankList() { //获取银行卡列表
				this.post('/api/finance/getUserBankList', {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.cardListBank = data;
						for(let i in this.cardListBank) {
							if(this.cardListBank[i].is_default) {
								this.cardId = this.cardListBank[i].id;
							}
						}
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			getUserBalance() {
				this.post('/api/finance/getUserBalance', {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.balanceList = data;
						for(let i = 0; i < this.balanceList.length; i++) {
							
							if(this.balanceList[i].type === 1) {
								this.balanceTypeOne.type = this.balanceList[i].type;
								this.balanceTypeOne.converge_balance = this.balanceList[i].purchasing_balance; //汇聚余额，要单独取出来
							} else if(this.balanceList[i].type === 2) {
								this.balanceTypeOne.purchasing_balance = this.balanceList[i].purchasing_balance //站内余额
								this.balanceTypeTwo.settlement_balance = this.balanceList[i].settlement_balance //结算余额(结算余额只使用type=2的即可)
								this.balanceTypeTwo.type = this.balanceList[i].type //1汇聚余额 2站内余额
							}
						}
						this.balanceTypeOne.converge_balance = this.toYuan(this.balanceTypeOne.converge_balance);
						this.balanceTypeOne.purchasing_balance = this.toYuan(this.balanceTypeOne.purchasing_balance);
						this.balanceTypeTwo.settlement_balance = this.toYuan(this.balanceTypeTwo.settlement_balance);
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					// console.log(Error);
				})
			},
			// 搜索余额明细数据
			searchList() {
				this.walletPage= 1
				this.walletList = []
				this.chooseTimes = this.times.toString()
				this.chooseTimee = this.timee.toString()
				this.getPurchaseBalanceList()
			},
		}
	}
</script>
<style scoped>
	.converge-bg .balance-wrap {
		background-image: url(https://mini-app-img-**********.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/balance.png) ;
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.account-bg .balance-wrap{
		background-image: url(https://mini-app-img-**********.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/balance2.png) ;
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.income-bg .balance-wrap {
		background-image: url(https://mini-app-img-**********.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/balance3.png) ;
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.wallet-tab-bg ::v-deep .u-tabs__wrapper__nav__item__text {
		font-size: 26rpx;
	}
	.date-time ::v-deep .uni-date-x[data-v-6e13d7e2] {
		height: 60rpx;
	}
	::v-deep .uni-select__input-box {
		width: 280rpx;
	}
	.select ::v-deep .uni-select__input-box[data-v-6b64008e] {
		height: 60rpx;
		width: 280rpx;
		min-height: 0rpx;
	}
	.search {
		height: 60rpx;
		width: 160rpx;
		margin-left: 52rpx;
	}
</style>
<style lang="scss" scoped>
	#wallet {
		position: relative;
		.walletTitle {
			font-size: 32rpx;
			color: #333333;
			line-height: 92rpx;
			padding-bottom: 40rpx;
			margin-left: 30rpx;
		}
		.balance {
			position: relative;
			background-color: #fff;
			// height: 225rpx;
			
			.balance-wrap {
				position: relative;
				top:-40rpx;
				width: calc(100% - 60rpx);
				height: 225rpx;
				margin: 0 30rpx;
				
				color: #fff;
				.converge {
					margin: 0 140rpx 0 72rpx;
				}
				.title {
					margin-bottom: 37rpx;
					font-size: 26rpx;
				}
				.money {
					font-size: 40rpx;
				}
			}
			.balance-cell {
				position: relative;
				// top:-10rpx;
				top:-16rpx;
				background-color: #fff;
				.cell {
					// padding: 0 30rpx 28rpx 30rpx;
					padding: 20rpx 30rpx 16rpx 30rpx;
				}
			}
		}
		.not-mt{
			margin-top: 0;
			margin-bottom: 50px;
		}
	  .not-mb{
			margin-bottom: 0;
		}
		.hint {
			line-height: 83rpx;
			margin-left: 30rpx;
			font-size: 24rpx;
			color: #838383;
		}
		.wallet-content {
			margin: 0 20rpx;
			.select {
				padding: 24rpx 26rpx 0 26rpx;
				background-color: #fff;
			}
			.wallet-tab-bg {
				padding: 0 26rpx;
				background-color: #fff;
				
			}
			.scroll-view_H {
				white-space: nowrap;
			}
			.wallet-tab {
				// width:100%;
				padding: 0rpx 4rpx 0rpx 0rpx;
				border-bottom: 1px solid #f2f2f2;
				view {
					font-size: 26rpx;
					color: #292929;
					// margin-right: 112rpx;
				}
				.on {
					color:#f14e4e;
					border-bottom: 5rpx solid #f14e4e;
				}
			}
			.wallet-list {
				background-color: #fff;
				padding: 0 26rpx;
					.item {
						padding: 28rpx  0 26rpx;
						border-bottom: 1px solid #f2f2f2;
						&:last-of-type {
							border-bottom: none;
						}
					}
			}
		}
	}
	
	.wallet-list-bank {
		height: 100%;
		.card-content {
			padding: 30rpx 26rpx 0rpx 26rpx;
			background-color: #fff;
			.item {
				border-bottom: 1px solid #f2f2f2;
				&:last-of-type {
					border-bottom: none;
				}
			}
			.action {
				background-color: #fff;
				line-height: 60rpx;
				.action-del {
					margin-right: 57rpx;
				}
				.icon-ht_operation_delete {
					color:#696969;
					font-size: 24rpx;
					margin-right: 10rpx;
				}
				.icon-fontclass-xiugai {
					color:#696969;
					font-size: 24rpx;
					margin-right: 10rpx;
				}
				.add-icon {
					font-size: 34rpx;
					margin-right: 16rpx;
				}
			}
		}
		
	}
	.select0p ::v-deep .uni-stat__select{
		padding: 0 !important;
	}

	
</style>

